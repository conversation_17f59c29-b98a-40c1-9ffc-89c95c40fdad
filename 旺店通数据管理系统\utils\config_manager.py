"""
配置管理器
"""
import json
import os
from datetime import datetime
from typing import Dict, Any, Optional
from config import app_config

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "app_config.json"):
        self.config_file = config_file
        self.config_data = {}
        self.load_config()
    
    def load_config(self) -> bool:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
                return True
            else:
                # 使用默认配置
                self.config_data = self._get_default_config()
                self.save_config()
                return True
        except Exception as e:
            print(f"配置加载失败: {e}")
            self.config_data = self._get_default_config()
            return False
    
    def save_config(self) -> bool:
        """保存配置文件"""
        try:
            # 添加保存时间戳
            self.config_data['last_saved'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"配置保存失败: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        value = self.config_data
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """设置配置值"""
        keys = key.split('.')
        config = self.config_data
        
        # 创建嵌套字典结构
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def update(self, updates: Dict[str, Any]) -> None:
        """批量更新配置"""
        for key, value in updates.items():
            self.set(key, value)
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'api': {
                'auto_fetch_enabled': True,
                'fetch_interval_minutes': app_config.DEFAULT_INTERVAL_MINUTES,
                'daily_times': app_config.DEFAULT_DAILY_TIMES.copy(),
                'timer_mode': 'daily'
            },
            'paths': {
                'output_path': app_config.DEFAULT_OUTPUT_PATH,
                'archive_path': app_config.DEFAULT_ARCHIVE_PATH,
                'monthly_report_path': app_config.DEFAULT_MONTHLY_REPORT_PATH
            },
            'monitor': {
                'enabled': True,
                'auto_process': True,
                'auto_send': True
            },
            'wechat': {
                'contacts': [],
                'send_as_image': True,
                'send_statistics_message': True
            },
            'monthly': {
                'default_dimensions': ['owner', 'category', 'logistics'],
                'default_chart_types': ['bar', 'pie'],
                'auto_generate': False
            },
            'ui': {
                'window_width': app_config.WINDOW_WIDTH,
                'window_height': app_config.WINDOW_HEIGHT,
                'theme': 'light',
                'auto_start_monitor': False
            }
        }
    
    # 便捷方法
    def get_api_config(self) -> Dict[str, Any]:
        """获取API配置"""
        return self.get('api', {})
    
    def get_paths_config(self) -> Dict[str, Any]:
        """获取路径配置"""
        return self.get('paths', {})
    
    def get_monitor_config(self) -> Dict[str, Any]:
        """获取监控配置"""
        return self.get('monitor', {})
    
    def get_wechat_config(self) -> Dict[str, Any]:
        """获取微信配置"""
        return self.get('wechat', {})
    
    def get_monthly_config(self) -> Dict[str, Any]:
        """获取月度配置"""
        return self.get('monthly', {})
    
    def get_ui_config(self) -> Dict[str, Any]:
        """获取UI配置"""
        return self.get('ui', {})
    
    def set_api_config(self, config: Dict[str, Any]) -> None:
        """设置API配置"""
        self.update({f'api.{k}': v for k, v in config.items()})
    
    def set_paths_config(self, config: Dict[str, Any]) -> None:
        """设置路径配置"""
        self.update({f'paths.{k}': v for k, v in config.items()})
    
    def set_monitor_config(self, config: Dict[str, Any]) -> None:
        """设置监控配置"""
        self.update({f'monitor.{k}': v for k, v in config.items()})
    
    def set_wechat_config(self, config: Dict[str, Any]) -> None:
        """设置微信配置"""
        self.update({f'wechat.{k}': v for k, v in config.items()})
    
    def set_monthly_config(self, config: Dict[str, Any]) -> None:
        """设置月度配置"""
        self.update({f'monthly.{k}': v for k, v in config.items()})
    
    def set_ui_config(self, config: Dict[str, Any]) -> None:
        """设置UI配置"""
        self.update({f'ui.{k}': v for k, v in config.items()})
    
    def export_config(self, file_path: str) -> bool:
        """导出配置到指定文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"配置导出失败: {e}")
            return False
    
    def import_config(self, file_path: str) -> bool:
        """从指定文件导入配置"""
        try:
            if not os.path.exists(file_path):
                return False
            
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # 合并配置（保留现有配置的结构）
            self._merge_config(self.config_data, imported_config)
            return True
        except Exception as e:
            print(f"配置导入失败: {e}")
            return False
    
    def _merge_config(self, target: Dict, source: Dict) -> None:
        """递归合并配置"""
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._merge_config(target[key], value)
            else:
                target[key] = value
    
    def reset_to_default(self) -> None:
        """重置为默认配置"""
        self.config_data = self._get_default_config()
        self.save_config()
    
    def validate_config(self) -> Dict[str, List[str]]:
        """验证配置有效性"""
        errors = {}
        
        # 验证路径配置
        paths = self.get_paths_config()
        path_errors = []
        
        if 'output_path' in paths:
            output_dir = os.path.dirname(paths['output_path'])
            if output_dir and not os.path.exists(output_dir):
                path_errors.append(f"输出目录不存在: {output_dir}")
        
        if 'archive_path' in paths and paths['archive_path']:
            if not os.path.exists(paths['archive_path']):
                path_errors.append(f"货品档案文件不存在: {paths['archive_path']}")
        
        if path_errors:
            errors['paths'] = path_errors
        
        # 验证API配置
        api = self.get_api_config()
        api_errors = []
        
        if 'fetch_interval_minutes' in api:
            interval = api['fetch_interval_minutes']
            if not isinstance(interval, int) or interval < 1 or interval > 1440:
                api_errors.append("获取间隔必须在1-1440分钟之间")
        
        if 'daily_times' in api:
            times = api['daily_times']
            if not isinstance(times, list):
                api_errors.append("每日执行时间必须是列表格式")
            else:
                for time_str in times:
                    try:
                        datetime.strptime(time_str, '%H:%M')
                    except ValueError:
                        api_errors.append(f"无效的时间格式: {time_str}")
        
        if api_errors:
            errors['api'] = api_errors
        
        return errors

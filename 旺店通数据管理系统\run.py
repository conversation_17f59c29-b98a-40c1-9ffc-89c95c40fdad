#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
旺店通数据管理系统启动脚本
"""

import sys
import os
import subprocess
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    return True

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'pandas',
        'openpyxl', 
        'requests',
        'python-dotenv'
    ]
    
    optional_packages = [
        'wxauto',
        'matplotlib',
        'seaborn',
        'sv-ttk',
        'pillow'
    ]
    
    missing_required = []
    missing_optional = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_required.append(package)
    
    for package in optional_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_optional.append(package)
    
    if missing_required:
        print("错误: 缺少必需的依赖包:")
        for package in missing_required:
            print(f"  - {package}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_required)}")
        return False
    
    if missing_optional:
        print("警告: 缺少可选的依赖包，某些功能可能不可用:")
        for package in missing_optional:
            print(f"  - {package}")
        print("\n可选安装命令:")
        print(f"pip install {' '.join(missing_optional)}")
        print()
    
    return True

def check_config():
    """检查配置文件"""
    env_file = Path('.env')
    env_example = Path('.env.example')
    
    if not env_file.exists():
        if env_example.exists():
            print("提示: 未找到 .env 配置文件")
            print("请复制 .env.example 为 .env 并配置相关参数")
            print()
        else:
            print("警告: 未找到配置文件")
    
    return True

def install_dependencies():
    """安装依赖包"""
    print("正在安装依赖包...")
    
    try:
        # 安装必需依赖
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ])
        print("依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"依赖包安装失败: {e}")
        return False
    except FileNotFoundError:
        print("未找到 requirements.txt 文件")
        return False

def create_sample_data():
    """创建示例数据"""
    try:
        from create_sample_data import create_sample_data, create_goods_archive
        
        print("正在创建示例数据...")
        create_sample_data()
        create_goods_archive()
        print("示例数据创建完成")
        return True
    except Exception as e:
        print(f"示例数据创建失败: {e}")
        return False

def main():
    """主函数"""
    print("旺店通数据管理系统启动检查")
    print("=" * 40)
    
    # 检查Python版本
    if not check_python_version():
        return 1
    
    # 检查依赖包
    if not check_dependencies():
        response = input("是否自动安装缺少的依赖包? (y/n): ")
        if response.lower() == 'y':
            if not install_dependencies():
                return 1
        else:
            return 1
    
    # 检查配置
    check_config()
    
    # 询问是否创建示例数据
    if not os.path.exists('sample_data'):
        response = input("是否创建示例数据用于测试? (y/n): ")
        if response.lower() == 'y':
            create_sample_data()
    
    print("=" * 40)
    print("启动检查完成，正在启动程序...")
    print()
    
    # 启动主程序
    try:
        from main import main as app_main
        app_main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序启动失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

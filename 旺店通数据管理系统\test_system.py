#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
旺店通数据管理系统综合测试脚本
"""

import os
import sys
import unittest
from unittest.mock import Mock, patch
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class TestSystemIntegration(unittest.TestCase):
    """系统集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_data_dir = "test_data"
        os.makedirs(self.test_data_dir, exist_ok=True)
    
    def test_config_manager(self):
        """测试配置管理器"""
        from utils.config_manager import ConfigManager
        
        config = ConfigManager("test_config.json")
        
        # 测试设置和获取配置
        config.set('test.key', 'test_value')
        self.assertEqual(config.get('test.key'), 'test_value')
        
        # 测试默认值
        self.assertEqual(config.get('nonexistent.key', 'default'), 'default')
        
        # 测试保存和加载
        self.assertTrue(config.save_config())
        
        # 清理测试文件
        if os.path.exists("test_config.json"):
            os.remove("test_config.json")
    
    def test_data_processor(self):
        """测试数据处理器"""
        from core.data_processor import DataProcessor
        import pandas as pd
        
        # 创建测试数据
        test_data = [
            {
                'owner_name': '测试货主',
                'logistics_no': 'SF123456789',
                'details_list': [
                    {
                        'goods_name': '测试商品',
                        'num': 10,
                        'remark': '测试分类'
                    }
                ]
            }
        ]
        
        processor = DataProcessor()
        
        # 测试API数据处理
        output_file = f"{self.test_data_dir}/test_output.xlsx"
        record_count = processor.process_api_data(test_data, output_file)
        
        self.assertGreater(record_count, 0)
        self.assertTrue(os.path.exists(output_file))
        
        # 验证输出文件内容
        df = pd.read_excel(output_file)
        self.assertIn('货主', df.columns)
        self.assertIn('物流单号', df.columns)
        self.assertIn('货品数量', df.columns)
    
    def test_statistics_engine(self):
        """测试统计分析引擎"""
        from core.statistics import StatisticsEngine
        import pandas as pd
        
        # 创建测试数据
        test_df = pd.DataFrame({
            '货主': ['货主A', '货主B', '货主A'],
            '物流单号': ['SF123', 'JD456', 'YT789'],
            '货品数量': [10, 20, 15],
            '分类': ['电子', '服装', '电子']
        })
        
        engine = StatisticsEngine()
        
        # 测试物流统计
        logistics_stats = engine.analyze_logistics_numbers(test_df)
        self.assertIn('logistics_stats', logistics_stats)
        self.assertIn('total_count', logistics_stats)
        
        # 测试货主统计
        owner_stats = engine.analyze_owner_statistics(test_df)
        self.assertIn('owner_data', owner_stats)
        
        # 测试分类统计
        category_stats = engine.analyze_category_statistics(test_df)
        self.assertIn('category_data', category_stats)
    
    def test_monthly_report_generator(self):
        """测试月度报告生成器"""
        from core.monthly_report import MonthlyReportGenerator
        import pandas as pd
        
        # 创建测试数据
        test_data = pd.DataFrame({
            '货主': ['货主A'] * 10,
            '物流单号': [f'SF{i}' for i in range(10)],
            '货品数量': [i+1 for i in range(10)],
            '分类': ['电子'] * 10,
            '发货时间': pd.date_range('2024-06-01', periods=10, freq='D')
        })
        
        # 保存测试数据
        test_file = f"{self.test_data_dir}/test_monthly_data.xlsx"
        test_data.to_excel(test_file, index=False)
        
        generator = MonthlyReportGenerator()
        
        # 测试数据加载
        monthly_data = generator._load_monthly_data(test_file, 2024, 6)
        self.assertFalse(monthly_data.empty)
        
        # 测试分析生成
        analysis = generator._generate_analysis(monthly_data, ['owner', 'category'])
        self.assertIn('basic', analysis)
    
    @patch('core.api_client.requests.post')
    def test_api_client(self, mock_post):
        """测试API客户端"""
        from core.api_client import WDTAPIClient
        
        # 模拟API响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'status': 0,
            'data': {
                'order': [],
                'total_count': 0
            }
        }
        mock_post.return_value = mock_response
        
        client = WDTAPIClient()
        
        # 测试API调用
        result = client.query_stockout(
            start_consign_time='2024-01-01 00:00:00',
            end_consign_time='2024-01-01 23:59:59'
        )
        
        self.assertIn('order', result)
        self.assertIn('total_count', result)
    
    def test_file_monitor(self):
        """测试文件监控器"""
        from utils.file_monitor import FileMonitor
        import time
        
        # 创建测试文件
        test_file = f"{self.test_data_dir}/monitor_test.txt"
        with open(test_file, 'w') as f:
            f.write("initial content")
        
        callback_called = []
        
        def test_callback(file_path):
            callback_called.append(file_path)
        
        monitor = FileMonitor(test_file, test_callback)
        
        # 启动监控
        self.assertTrue(monitor.start_monitoring())
        self.assertTrue(monitor.is_monitoring())
        
        # 修改文件
        time.sleep(0.1)  # 确保监控器已启动
        with open(test_file, 'w') as f:
            f.write("modified content")
        
        # 等待回调
        time.sleep(3)  # 等待监控器检测到变化
        
        # 停止监控
        self.assertTrue(monitor.stop_monitoring())
        self.assertFalse(monitor.is_monitoring())
        
        # 验证回调被调用
        # 注意：由于时间窗口的原因，这个测试可能不稳定
        # self.assertGreater(len(callback_called), 0)
    
    def test_excel_utils(self):
        """测试Excel工具"""
        from utils.excel_utils import ExcelUtils
        import pandas as pd
        
        # 创建测试Excel文件
        test_data = pd.DataFrame({
            '货主': ['货主A', '货主B'],
            '物流单号': ['SF123', 'JD456'],
            '货品数量': [10, 20],
            '分类': ['电子', '服装']
        })
        
        test_file = f"{self.test_data_dir}/test_excel.xlsx"
        test_data.to_excel(test_file, index=False)
        
        # 测试文件验证
        validation_result = ExcelUtils.validate_excel_file(test_file)
        self.assertTrue(validation_result['valid'])
        
        # 测试文件信息获取
        file_info = ExcelUtils.get_excel_info(test_file)
        self.assertTrue(file_info['exists'])
        self.assertGreater(len(file_info['sheets']), 0)
    
    def tearDown(self):
        """测试后清理"""
        # 清理测试文件
        import shutil
        if os.path.exists(self.test_data_dir):
            shutil.rmtree(self.test_data_dir)

class TestSystemComponents(unittest.TestCase):
    """系统组件测试"""
    
    def test_imports(self):
        """测试所有模块导入"""
        try:
            from config import wdt_config, app_config, file_config, log_config
            from core import WDTAPIClient, DataProcessor, StatisticsEngine, WeChatSender, MonthlyReportGenerator
            from utils import ConfigManager, FileMonitor, ExcelUtils
            from gui import MainWindow, ConfigDialog, MonthlyDialog
        except ImportError as e:
            self.fail(f"模块导入失败: {e}")
    
    def test_config_values(self):
        """测试配置值"""
        from config import app_config, wdt_config
        
        # 测试应用配置
        self.assertIsInstance(app_config.APP_NAME, str)
        self.assertIsInstance(app_config.APP_VERSION, str)
        self.assertIsInstance(app_config.WINDOW_WIDTH, int)
        self.assertIsInstance(app_config.WINDOW_HEIGHT, int)
        
        # 测试API配置
        self.assertIsInstance(wdt_config.SID, str)
        self.assertIsInstance(wdt_config.APP_KEY, str)
        self.assertIsInstance(wdt_config.API_URL, str)

def run_performance_test():
    """运行性能测试"""
    print("=== 性能测试 ===")
    
    # 测试大数据量处理
    import pandas as pd
    from core.statistics import StatisticsEngine
    import time
    
    # 生成大量测试数据
    large_data = pd.DataFrame({
        '货主': [f'货主{i%10}' for i in range(10000)],
        '物流单号': [f'SF{i}' for i in range(10000)],
        '货品数量': [i%100 + 1 for i in range(10000)],
        '分类': [f'分类{i%5}' for i in range(10000)]
    })
    
    engine = StatisticsEngine()
    
    # 测试物流统计性能
    start_time = time.time()
    logistics_stats = engine.analyze_logistics_numbers(large_data)
    end_time = time.time()
    
    print(f"物流统计处理 10,000 条记录耗时: {end_time - start_time:.2f} 秒")
    print(f"统计结果: {logistics_stats['unique_count']} 个唯一物流单号")
    
    # 测试货主统计性能
    start_time = time.time()
    owner_stats = engine.analyze_owner_statistics(large_data)
    end_time = time.time()
    
    print(f"货主统计处理 10,000 条记录耗时: {end_time - start_time:.2f} 秒")
    print(f"统计结果: {owner_stats['total_owners']} 个货主")

def run_integration_test():
    """运行集成测试"""
    print("=== 集成测试 ===")
    
    # 测试完整的数据处理流程
    try:
        from create_sample_data import create_sample_data
        from core.monthly_report import MonthlyReportGenerator
        
        print("1. 创建示例数据...")
        create_sample_data()
        
        print("2. 测试月度报告生成...")
        generator = MonthlyReportGenerator()
        
        result = generator.generate_monthly_report(
            data_source="sample_data",
            year=2024,
            month=6,
            output_dir="integration_test_output",
            dimensions=['owner', 'category'],
            chart_types=['bar'] if generator.is_chart_available() else []
        )
        
        if result['success']:
            print(f"✅ 月度报告生成成功: {result['message']}")
            print(f"生成文件: {len(result['files'])} 个")
        else:
            print(f"❌ 月度报告生成失败: {result['message']}")
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")

if __name__ == "__main__":
    print("旺店通数据管理系统测试套件")
    print("=" * 50)
    
    # 运行单元测试
    print("运行单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n" + "=" * 50)
    
    # 运行性能测试
    run_performance_test()
    
    print("\n" + "=" * 50)
    
    # 运行集成测试
    run_integration_test()
    
    print("\n测试完成！")

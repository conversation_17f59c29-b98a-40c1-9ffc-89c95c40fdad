#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
旺店通数据管理系统 - 主程序
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import app_config, log_config
from utils.config_manager import ConfigManager
from core.api_client import WDTAPIClient
from core.data_processor import DataProcessor
from core.statistics import StatisticsEngine
from core.wechat_sender import WeChatSender
from core.monthly_report import MonthlyReportGenerator
from utils.file_monitor import FileMonitor
from gui.main_window import MainWindow

# 检查可选依赖
try:
    import sv_ttk
    THEME_AVAILABLE = True
except ImportError:
    THEME_AVAILABLE = False

class WDTDataManagementSystem:
    """旺店通数据管理系统主类"""
    
    def __init__(self):
        self.root = None
        self.main_window = None
        self.config_manager = ConfigManager()
        
        # 核心组件
        self.api_client = WDTAPIClient()
        self.data_processor = DataProcessor()
        self.statistics_engine = StatisticsEngine()
        self.wechat_sender = WeChatSender()
        self.monthly_report_generator = MonthlyReportGenerator()
        self.file_monitor = None
        
        # 状态变量
        self.api_timer_running = False
        self.file_monitoring = False
        
        # 设置日志
        self._setup_logging()
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("旺店通数据管理系统初始化完成")
    
    def _setup_logging(self):
        """设置日志系统"""
        # 创建日志目录
        log_dir = os.path.dirname(log_config.LOG_FILE)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format=log_config.LOG_FORMAT,
            datefmt=log_config.DATE_FORMAT,
            handlers=[
                logging.FileHandler(log_config.LOG_FILE, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    
    def initialize_gui(self):
        """初始化GUI"""
        self.root = tk.Tk()
        self.root.title(app_config.APP_NAME)
        self.root.geometry(f"{app_config.WINDOW_WIDTH}x{app_config.WINDOW_HEIGHT}")
        
        # 应用主题（如果可用）
        if THEME_AVAILABLE:
            try:
                theme = self.config_manager.get('ui.theme', 'light')
                sv_ttk.set_theme(theme)
            except Exception as e:
                self.logger.warning(f"主题设置失败: {e}")
        
        # 创建主窗口
        self.main_window = MainWindow(self.root, self)
        
        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        
        # 自动启动功能（如果配置了）
        self.root.after(1000, self._auto_start_features)
    
    def _auto_start_features(self):
        """自动启动功能"""
        try:
            # 自动启动文件监控
            if self.config_manager.get('ui.auto_start_monitor', False):
                self.start_file_monitoring()
            
            # 自动启动API定时任务
            if self.config_manager.get('api.auto_fetch_enabled', False):
                self.start_api_timer()
                
        except Exception as e:
            self.logger.error(f"自动启动功能失败: {e}")
    
    def start_api_timer(self):
        """启动API定时任务"""
        if self.api_timer_running:
            return False
        
        try:
            self.api_timer_running = True
            timer_thread = threading.Thread(target=self._api_timer_loop, daemon=True)
            timer_thread.start()
            
            self.logger.info("API定时任务已启动")
            return True
            
        except Exception as e:
            self.logger.error(f"启动API定时任务失败: {e}")
            self.api_timer_running = False
            return False
    
    def stop_api_timer(self):
        """停止API定时任务"""
        self.api_timer_running = False
        self.logger.info("API定时任务已停止")
    
    def start_file_monitoring(self):
        """启动文件监控"""
        if self.file_monitoring:
            return False
        
        try:
            file_path = self.config_manager.get('paths.output_path')
            if not file_path:
                return False
            
            self.file_monitor = FileMonitor(file_path, self._on_file_changed)
            self.file_monitor.start_monitoring()
            self.file_monitoring = True
            
            self.logger.info(f"文件监控已启动: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"启动文件监控失败: {e}")
            return False
    
    def stop_file_monitoring(self):
        """停止文件监控"""
        if self.file_monitor:
            self.file_monitor.stop_monitoring()
            self.file_monitor = None
        
        self.file_monitoring = False
        self.logger.info("文件监控已停止")
    
    def _api_timer_loop(self):
        """API定时任务循环"""
        import time
        
        while self.api_timer_running:
            try:
                # 执行数据获取
                self._execute_api_fetch()
                
                # 等待下次执行
                interval = self.config_manager.get('api.fetch_interval_minutes', 60) * 60
                time.sleep(interval)
                
            except Exception as e:
                self.logger.error(f"API定时任务执行失败: {e}")
                time.sleep(60)  # 出错时等待1分钟后重试
    
    def _execute_api_fetch(self):
        """执行API数据获取"""
        try:
            # 获取当天数据
            start_time = datetime.now().strftime('%Y-%m-%d 00:00:00')
            end_time = datetime.now().strftime('%Y-%m-%d 23:59:59')
            
            # 调用API获取数据
            api_data = self.api_client.fetch_all_stockout_data(
                start_consign_time=start_time,
                end_consign_time=end_time,
                progress_callback=self._log_progress
            )
            
            if api_data:
                # 设置货品档案
                archive_path = self.config_manager.get('paths.archive_path')
                if archive_path:
                    self.data_processor.set_archive_file(archive_path)
                
                # 处理数据
                output_path = self.config_manager.get('paths.output_path')
                record_count = self.data_processor.process_api_data(
                    api_data, output_path, self._log_progress
                )
                
                self.logger.info(f"API数据获取完成，处理了 {record_count} 条记录")
                
                # 通知主窗口更新
                if self.main_window:
                    self.main_window.on_api_fetch_completed(record_count)
            
        except Exception as e:
            self.logger.error(f"API数据获取失败: {e}")
            if self.main_window:
                self.main_window.on_api_fetch_failed(str(e))
    
    def _on_file_changed(self, file_path: str):
        """文件变化回调"""
        try:
            self.logger.info(f"检测到文件变化: {file_path}")
            
            # 检查是否启用自动处理
            if not self.config_manager.get('monitor.auto_process', True):
                return
            
            # 处理数据
            output_file, logistics_stats = self.data_processor.create_pivot_table(file_path)
            
            self.logger.info(f"数据处理完成: {output_file}")
            
            # 检查是否启用自动发送
            if self.config_manager.get('monitor.auto_send', True):
                self._auto_send_results(output_file, logistics_stats)
            
            # 通知主窗口更新
            if self.main_window:
                self.main_window.on_file_processed(output_file, logistics_stats)
                
        except Exception as e:
            self.logger.error(f"文件处理失败: {e}")
            if self.main_window:
                self.main_window.on_file_process_failed(str(e))
    
    def _auto_send_results(self, file_path: str, logistics_stats: dict):
        """自动发送结果"""
        try:
            contacts = self.config_manager.get('wechat.contacts', [])
            if not contacts:
                return
            
            # 发送统计消息
            if self.config_manager.get('wechat.send_statistics_message', True):
                message = logistics_stats.get('message', '')
                if message:
                    for contact in contacts:
                        self.wechat_sender.send_message(contact, message)
            
            # 发送文件
            send_as_image = self.config_manager.get('wechat.send_as_image', True)
            
            if send_as_image:
                # 转换为图片并发送
                from core.wechat_sender import ExcelToImageConverter
                converter = ExcelToImageConverter()
                if converter.is_available():
                    try:
                        image_path = converter.convert_simple(file_path)
                        for contact in contacts:
                            self.wechat_sender.send_file(contact, image_path)
                    except Exception as e:
                        # 转换失败，发送原文件
                        for contact in contacts:
                            self.wechat_sender.send_file(contact, file_path)
                else:
                    # 不支持转换，发送原文件
                    for contact in contacts:
                        self.wechat_sender.send_file(contact, file_path)
            else:
                # 直接发送Excel文件
                for contact in contacts:
                    self.wechat_sender.send_file(contact, file_path)
            
            self.logger.info("自动发送完成")
            
        except Exception as e:
            self.logger.error(f"自动发送失败: {e}")
    
    def _log_progress(self, message: str):
        """进度日志回调"""
        self.logger.info(message)
        if self.main_window:
            self.main_window.log_message(message)
    
    def _on_closing(self):
        """程序关闭事件"""
        try:
            # 停止所有任务
            self.stop_api_timer()
            self.stop_file_monitoring()
            
            # 保存配置
            self.config_manager.save_config()
            
            self.logger.info("程序正常退出")
            
        except Exception as e:
            self.logger.error(f"程序退出时出错: {e}")
        finally:
            self.root.destroy()
    
    def run(self):
        """运行程序"""
        try:
            self.initialize_gui()
            self.root.mainloop()
        except Exception as e:
            self.logger.error(f"程序运行失败: {e}")
            messagebox.showerror("错误", f"程序运行失败: {e}")

def main():
    """主函数"""
    try:
        app = WDTDataManagementSystem()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        messagebox.showerror("启动失败", f"程序启动失败: {e}")

if __name__ == "__main__":
    main()

"""
旺店通数据导出模块
"""
import pandas as pd
import os
from datetime import datetime
from typing import Optional, Callable
from core.api_client import WDTAPIClient
from core.data_processor import DataProcessor

def export_to_wdt_data(
    target_path: str,
    archive_file: str,
    log_callback: Optional[Callable] = None,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None
) -> int:
    """
    导出旺店通数据到Excel文件
    
    Args:
        target_path: 目标Excel文件路径
        archive_file: 货品档案文件路径
        log_callback: 日志回调函数
        start_time: 开始时间（可选，默认为当天）
        end_time: 结束时间（可选，默认为当天）
        
    Returns:
        导出的记录数
    """
    try:
        if log_callback:
            log_callback("开始导出旺店通数据...")
        
        # 设置默认时间范围（当天）
        if not start_time:
            start_time = datetime.now().strftime('%Y-%m-%d 00:00:00')
        if not end_time:
            end_time = datetime.now().strftime('%Y-%m-%d 23:59:59')
        
        if log_callback:
            log_callback(f"时间范围: {start_time} 到 {end_time}")
        
        # 初始化API客户端
        api_client = WDTAPIClient()
        
        # 测试API连接
        if not api_client.test_connection():
            raise Exception("API连接测试失败")
        
        if log_callback:
            log_callback("API连接正常，开始获取数据...")
        
        # 获取出库数据
        api_data = api_client.fetch_all_stockout_data(
            start_consign_time=start_time,
            end_consign_time=end_time,
            progress_callback=log_callback
        )
        
        if not api_data:
            if log_callback:
                log_callback("未获取到任何数据")
            return 0
        
        if log_callback:
            log_callback(f"获取到 {len(api_data)} 条API数据，开始处理...")
        
        # 初始化数据处理器
        data_processor = DataProcessor(archive_file)
        
        # 处理并导出数据
        record_count = data_processor.process_api_data(
            api_data, target_path, log_callback
        )
        
        if log_callback:
            log_callback(f"数据导出完成，共处理 {record_count} 条记录")
        
        return record_count
        
    except Exception as e:
        error_msg = f"数据导出失败: {str(e)}"
        if log_callback:
            log_callback(error_msg)
        raise Exception(error_msg)

def export_to_wdt_data_simple(target_path: str, archive_file: str) -> int:
    """
    简化版数据导出（无回调）
    
    Args:
        target_path: 目标Excel文件路径
        archive_file: 货品档案文件路径
        
    Returns:
        导出的记录数
    """
    return export_to_wdt_data(target_path, archive_file)

def validate_export_params(target_path: str, archive_file: str) -> bool:
    """
    验证导出参数
    
    Args:
        target_path: 目标Excel文件路径
        archive_file: 货品档案文件路径
        
    Returns:
        参数是否有效
    """
    try:
        # 检查货品档案文件
        if not os.path.exists(archive_file):
            print(f"货品档案文件不存在: {archive_file}")
            return False
        
        # 检查目标路径的目录
        target_dir = os.path.dirname(target_path)
        if target_dir and not os.path.exists(target_dir):
            try:
                os.makedirs(target_dir, exist_ok=True)
            except Exception as e:
                print(f"无法创建目标目录: {e}")
                return False
        
        # 检查文件扩展名
        if not target_path.lower().endswith(('.xlsx', '.xls')):
            print("目标文件必须是Excel格式")
            return False
        
        return True
        
    except Exception as e:
        print(f"参数验证失败: {e}")
        return False

if __name__ == "__main__":
    # 测试导出功能
    print("测试旺店通数据导出功能...")
    
    target_file = "test_output.xlsx"
    archive_file = "货品档案.xlsx"
    
    if validate_export_params(target_file, archive_file):
        try:
            record_count = export_to_wdt_data(
                target_path=target_file,
                archive_file=archive_file,
                log_callback=print
            )
            print(f"导出成功，共 {record_count} 条记录")
        except Exception as e:
            print(f"导出失败: {e}")
    else:
        print("参数验证失败")

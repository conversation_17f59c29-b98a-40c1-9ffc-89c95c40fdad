@echo off
chcp 65001 >nul
echo ================================================================
echo 旺店通数据管理系统 - 自动安装所有依赖包
echo ================================================================
echo.

echo 正在安装核心依赖包...
echo --------------------------------
pip install --user pandas>=1.5.0
pip install --user openpyxl>=3.0.0
pip install --user numpy>=1.21.0
pip install --user requests>=2.28.0
pip install --user python-dotenv>=0.19.0

echo.
echo 正在安装图表功能依赖包...
echo --------------------------------
pip install --user matplotlib>=3.5.0
pip install --user seaborn>=0.11.0

echo.
echo 正在安装图像处理依赖包...
echo --------------------------------
pip install --user pillow>=9.0.0

echo.
echo 正在安装微信自动化依赖包...
echo --------------------------------
pip install --user wxauto>=3.9.0

echo.
echo 正在安装GUI主题依赖包...
echo --------------------------------
pip install --user sv-ttk>=2.0.0

echo.
echo 正在安装Windows系统依赖包...
echo --------------------------------
pip install --user pywin32>=304

echo.
echo 正在安装系统工具依赖包...
echo --------------------------------
pip install --user psutil>=5.9.0

echo.
echo ================================================================
echo 安装完成！
echo ================================================================
echo.
echo 现在可以运行程序了：
echo python main.py
echo.
echo 或者运行启动脚本：
echo python run.py
echo.
pause

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建示例数据用于测试月度统计功能
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime, timedelta
import random

def create_sample_data():
    """创建示例出货数据"""
    
    # 设置随机种子以获得可重复的结果
    np.random.seed(42)
    random.seed(42)
    
    # 定义基础数据
    owners = ['货主A', '货主B', '货主C', '货主D', '货主E']
    categories = ['电子产品', '服装', '食品', '日用品', '化妆品', '图书']
    logistics_companies = ['SF', 'JD', 'YT', 'ZT', 'ST', 'YD', 'EMS']
    
    # 生成2024年全年的数据
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 12, 31)
    
    all_data = []
    
    # 按月生成数据
    current_date = start_date
    while current_date <= end_date:
        # 每月生成200-500条记录
        daily_records = random.randint(200, 500)
        
        for _ in range(daily_records):
            # 随机选择货主和分类
            owner = random.choice(owners)
            category = random.choice(categories)
            
            # 生成物流单号
            logistics_prefix = random.choice(logistics_companies)
            logistics_no = f"{logistics_prefix}{random.randint(100000000000, 999999999999)}"
            
            # 生成货品编号
            goods_no = f"G{random.randint(10000, 99999)}"
            
            # 生成货品数量（1-50）
            goods_num = random.randint(1, 50)
            
            # 随机日期（当月内）
            if current_date.month == 12:
                next_month = datetime(current_date.year + 1, 1, 1)
            else:
                next_month = datetime(current_date.year, current_date.month + 1, 1)
            
            days_in_month = (next_month - current_date).days
            random_day = random.randint(0, days_in_month - 1)
            ship_date = current_date + timedelta(days=random_day)
            
            # 添加随机时间
            random_hour = random.randint(8, 18)
            random_minute = random.randint(0, 59)
            ship_datetime = ship_date.replace(hour=random_hour, minute=random_minute)
            
            record = {
                '货主': owner,
                '货品编号': goods_no,
                '物流单号': logistics_no,
                '货品数量': goods_num,
                '分类': category,
                '发货时间': ship_datetime
            }
            
            all_data.append(record)
        
        # 移动到下个月
        if current_date.month == 12:
            current_date = datetime(current_date.year + 1, 1, 1)
        else:
            current_date = datetime(current_date.year, current_date.month + 1, 1)
    
    # 创建DataFrame
    df = pd.DataFrame(all_data)
    
    # 确保输出目录存在
    output_dir = "sample_data"
    os.makedirs(output_dir, exist_ok=True)
    
    # 按月保存数据文件
    for month in range(1, 13):
        month_data = df[df['发货时间'].dt.month == month].copy()
        if not month_data.empty:
            filename = f"{output_dir}/2024年{month:02d}月出货数据.xlsx"
            month_data.to_excel(filename, index=False)
            print(f"已生成: {filename} ({len(month_data)} 条记录)")
    
    # 生成全年汇总数据
    all_filename = f"{output_dir}/2024年全年出货数据.xlsx"
    df.to_excel(all_filename, index=False)
    print(f"已生成: {all_filename} ({len(df)} 条记录)")
    
    # 生成统计摘要
    print("\n数据统计摘要:")
    print(f"总记录数: {len(df)}")
    print(f"时间范围: {df['发货时间'].min()} 到 {df['发货时间'].max()}")
    print(f"货主数量: {df['货主'].nunique()}")
    print(f"分类数量: {df['分类'].nunique()}")
    print(f"唯一物流单号: {df['物流单号'].nunique()}")
    
    # 按月统计
    monthly_stats = df.groupby(df['发货时间'].dt.month).agg({
        '货品数量': 'sum',
        '物流单号': 'nunique'
    })
    print("\n按月统计:")
    for month, stats in monthly_stats.iterrows():
        print(f"{month:2d}月: 数量={stats['货品数量']:,}, 单号={stats['物流单号']:,}")
    
    return df

def create_goods_archive():
    """创建货品档案文件"""
    
    # 生成货品档案数据
    goods_data = []
    categories = ['电子产品', '服装', '食品', '日用品', '化妆品', '图书']
    
    for i in range(1000, 2000):  # 生成1000个货品
        goods_name = f"商品{i}"
        goods_no = f"G{i}"
        category = random.choice(categories)
        
        goods_data.append({
            '货品名称': goods_name,
            '货品编号': goods_no,
            '备注': category
        })
    
    # 保存货品档案
    df = pd.DataFrame(goods_data)
    filename = "货品档案.xlsx"
    df.to_excel(filename, index=False)
    print(f"已生成货品档案: {filename} ({len(df)} 条记录)")
    
    return df

if __name__ == "__main__":
    print("正在生成示例数据...")
    
    # 创建示例出货数据
    sample_data = create_sample_data()
    
    # 创建货品档案
    goods_archive = create_goods_archive()
    
    print("\n示例数据生成完成！")
    print("\n使用说明:")
    print("1. sample_data/ 目录包含按月分割的出货数据")
    print("2. 货品档案.xlsx 包含货品信息")
    print("3. 可以使用这些数据测试月度统计功能")
    print("4. 在月度统计对话框中选择 sample_data 目录作为数据源")

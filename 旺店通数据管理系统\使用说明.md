# 旺店通数据管理系统使用说明

## 系统概述

旺店通数据管理系统是一个集成的数据管理解决方案，融合了以下功能：

- **API数据获取**: 自动从旺店通API获取出库数据
- **数据处理与分析**: 智能处理数据，生成统计报告
- **微信自动发送**: 自动发送报告到微信联系人
- **月度统计报告**: 生成可自定义的月度数据分析报告

## 快速开始

### 1. 环境准备

**系统要求:**
- Windows 10/11
- Python 3.8+
- 微信客户端（用于自动发送功能）

**安装步骤:**
1. 下载并解压系统文件
2. 运行 `python run.py` 进行自动安装和配置
3. 或手动安装依赖: `pip install -r requirements.txt`

### 2. 配置设置

**API配置:**
1. 复制 `.env.example` 为 `.env`
2. 填入旺店通API参数:
   ```
   WDT_SID=你的SID
   WDT_APP_KEY=你的APP_KEY
   WDT_APP_SECRET=你的APP_SECRET
   ```

**货品档案:**
- 准备Excel格式的货品档案文件
- 必须包含"货品名称"列
- 可选包含"货品编号"和"备注"列

### 3. 启动程序

运行以下命令之一：
```bash
python run.py          # 推荐，包含启动检查
python main.py          # 直接启动
```

## 功能详解

### API数据获取

**功能说明:**
- 定时从旺店通API获取出库数据
- 支持间隔定时和每日定时两种模式
- 自动处理分页数据
- 智能匹配货品档案信息

**使用步骤:**
1. 在"API设置"区域配置输出路径和货品档案
2. 点击"测试连接"验证API配置
3. 点击"启动定时任务"开始自动获取
4. 或点击"立即获取"手动获取数据

**配置选项:**
- **输出路径**: 数据保存的Excel文件路径
- **货品档案**: 用于匹配货品信息的Excel文件
- **获取间隔**: 定时任务的执行间隔（分钟）
- **执行时间**: 每日定时模式的执行时间点

### 文件监控与处理

**功能说明:**
- 监控指定Excel文件的变化
- 自动生成数据透视表和统计分析
- 智能识别物流公司并统计单量
- 自动发送处理结果到微信

**使用步骤:**
1. 在"监控设置"区域选择要监控的文件
2. 设置微信联系人（用分号分隔多个联系人）
3. 配置自动处理和发送选项
4. 点击"开始监控"启动文件监控
5. 或点击"立即处理"手动处理文件

**配置选项:**
- **监控文件**: 要监控变化的Excel文件
- **微信联系人**: 接收报告的微信联系人
- **自动处理**: 文件变化时自动处理数据
- **自动发送**: 处理完成后自动发送结果
- **发送图片**: 将Excel转换为图片格式发送

### 月度统计报告

**功能说明:**
- 生成指定月份的出货数据统计报告
- 支持多维度数据分析
- 自动生成可视化图表
- 导出Excel和图片格式报告

**使用步骤:**
1. 点击"生成月度报告"打开统计对话框
2. 选择统计年份和月份
3. 选择数据源（文件或目录）
4. 选择统计维度和图表类型
5. 设置输出目录
6. 点击"生成报告"开始生成

**统计维度:**
- **货主**: 按货主统计出货数据
- **分类**: 按商品分类统计
- **物流公司**: 按物流公司统计单量
- **日期**: 按日期统计趋势

**图表类型:**
- **柱状图**: 适合比较不同类别的数据
- **折线图**: 适合显示趋势变化
- **饼图**: 适合显示比例分布
- **面积图**: 适合显示累积数据

## 高级功能

### 配置管理

**访问方式:** 点击"配置设置"按钮

**功能包括:**
- **API设置**: 配置获取间隔、定时模式等
- **路径设置**: 管理各种文件路径
- **监控设置**: 配置文件监控选项
- **微信设置**: 管理联系人和发送选项
- **界面设置**: 主题和启动选项

**配置导入导出:**
- 支持导出当前配置为JSON文件
- 支持从JSON文件导入配置
- 支持重置为默认配置

### 数据处理流程

**API数据处理:**
1. 从旺店通API获取原始出库数据
2. 解析订单和商品明细信息
3. 匹配货品档案获取分类信息
4. 生成标准格式的Excel文件

**文件监控处理:**
1. 检测Excel文件变化
2. 读取并验证数据格式
3. 生成按货主和分类的数据透视表
4. 统计物流单号并识别快递公司
5. 美化Excel格式并保存
6. 发送统计消息和文件到微信

### 微信发送功能

**前置条件:**
- 微信客户端已登录
- 联系人名称与微信显示完全一致
- 安装了wxauto依赖包

**发送内容:**
- 物流统计消息（文本）
- Excel数据文件
- 图片格式报告（可选）

**注意事项:**
- 首次使用建议先测试连接
- 确保微信在前台运行
- 避免在发送过程中操作微信

## 故障排除

### 常见问题

**API连接失败:**
- 检查网络连接
- 验证API参数配置
- 确认API权限和配额

**文件处理失败:**
- 确保Excel文件未被其他程序占用
- 检查文件格式和必需列
- 验证文件路径是否正确

**微信发送失败:**
- 确认微信客户端已登录
- 检查联系人名称是否正确
- 重启微信客户端后重试

**图表生成失败:**
- 安装matplotlib和seaborn: `pip install matplotlib seaborn`
- 检查数据是否包含必要的列
- 确认输出目录有写入权限

### 日志查看

**实时日志:**
- 程序界面底部显示实时运行日志
- 不同级别的消息用不同颜色显示
- 支持清空和保存日志

**日志文件:**
- 详细日志保存在 `logs/wdt_system.log`
- 包含错误堆栈和调试信息
- 自动轮转，避免文件过大

### 性能优化

**大数据量处理:**
- 调整API批量大小参数
- 增加请求间隔避免频率限制
- 分批处理大型Excel文件

**内存使用:**
- 定期清理临时文件
- 关闭不必要的功能模块
- 重启程序释放内存

## 技术支持

**获取帮助:**
- 查看程序内置帮助文档
- 检查日志文件中的错误信息
- 联系技术支持团队

**反馈问题:**
- 提供详细的错误描述
- 附上相关日志文件
- 说明操作步骤和环境信息

**版本更新:**
- 定期检查新版本发布
- 备份配置文件后再更新
- 阅读更新日志了解新功能

"""
数据处理模块
"""
import pandas as pd
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils.dataframe import dataframe_to_rows
import os
import time
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from config import file_config, app_config

class GoodsArchiveMapper:
    """货品档案映射器"""
    
    def __init__(self, archive_file: str):
        self.archive_file = archive_file
        self.goods_map = {}
        self.load_goods_archive()
    
    def load_goods_archive(self) -> bool:
        """加载货品档案"""
        try:
            if not os.path.exists(self.archive_file):
                return False
            
            # 读取Excel文件
            wb = openpyxl.load_workbook(self.archive_file, read_only=True)
            ws = wb.active
            
            # 获取表头
            headers = [cell.value for cell in ws[1]]
            
            # 查找必要的列索引
            goods_name_idx = None
            goods_no_idx = None
            goods_remark_idx = None
            
            for i, header in enumerate(headers):
                if header and '货品名称' in str(header):
                    goods_name_idx = i
                elif header and '货品编号' in str(header):
                    goods_no_idx = i
                elif header and '备注' in str(header):
                    goods_remark_idx = i
            
            if goods_name_idx is None:
                return False
            
            # 读取数据行
            for row in ws.iter_rows(min_row=2, values_only=True):
                if not row or not any(row):
                    continue
                
                goods_name = ''
                if goods_name_idx < len(row) and row[goods_name_idx]:
                    goods_name = str(row[goods_name_idx]).strip()
                
                if not goods_name:
                    continue
                
                goods_no = ''
                if goods_no_idx is not None and goods_no_idx < len(row) and row[goods_no_idx]:
                    goods_no = str(row[goods_no_idx]).strip()
                
                goods_remark = ''
                if goods_remark_idx is not None and goods_remark_idx < len(row) and row[goods_remark_idx]:
                    goods_remark = str(row[goods_remark_idx]).strip()
                
                self.goods_map[goods_name] = {
                    'goods_no': goods_no,
                    'goods_remark': goods_remark
                }
            
            wb.close()
            return True
            
        except Exception as e:
            return False
    
    def get_goods_info(self, goods_name: str) -> Dict[str, str]:
        """获取货品信息"""
        return self.goods_map.get(goods_name, {'goods_no': '', 'goods_remark': ''})

class DataProcessor:
    """数据处理引擎"""
    
    def __init__(self, archive_file: Optional[str] = None):
        self.archive_mapper = None
        if archive_file:
            self.archive_mapper = GoodsArchiveMapper(archive_file)
    
    def set_archive_file(self, archive_file: str):
        """设置货品档案文件"""
        self.archive_mapper = GoodsArchiveMapper(archive_file)
    
    def process_api_data(self, api_data: List[Dict], target_path: str, log_callback: Optional[callable] = None) -> int:
        """
        处理API数据并导出到Excel
        
        Args:
            api_data: API返回的数据列表
            target_path: 目标Excel文件路径
            log_callback: 日志回调函数
            
        Returns:
            处理的记录数
        """
        try:
            if log_callback:
                log_callback(f"开始处理 {len(api_data)} 条API数据...")
            
            # 转换数据格式
            processed_data = []
            
            for order in api_data:
                # 获取订单基本信息
                owner_name = order.get('owner_name', '')
                logistics_no = order.get('logistics_no', '')
                
                # 处理商品明细
                details_list = order.get('details_list', [])
                for detail in details_list:
                    goods_name = detail.get('goods_name', '')
                    goods_num = detail.get('num', 0)
                    goods_remark = detail.get('remark', '')
                    
                    # 从货品档案获取信息
                    goods_info = {'goods_no': '', 'goods_remark': ''}
                    if self.archive_mapper:
                        goods_info = self.archive_mapper.get_goods_info(goods_name)
                    
                    # 确定分类（优先使用商品备注，其次使用档案备注）
                    category = goods_remark if goods_remark else goods_info.get('goods_remark', '')
                    
                    processed_data.append({
                        '货主': owner_name,
                        '货品编号': goods_info.get('goods_no', ''),
                        '物流单号': logistics_no,
                        '货品数量': goods_num,
                        '分类': category
                    })
            
            # 创建DataFrame
            df = pd.DataFrame(processed_data)
            
            # 导出到Excel
            self._export_to_excel(df, target_path)
            
            if log_callback:
                log_callback(f"数据处理完成，共处理 {len(processed_data)} 条记录")
            
            return len(processed_data)
            
        except Exception as e:
            if log_callback:
                log_callback(f"数据处理失败: {e}")
            raise
    
    def create_pivot_table(self, file_path: str, max_retries: int = 3, retry_delay: int = 2) -> Tuple[str, Dict]:
        """
        创建数据透视表
        
        Args:
            file_path: 源Excel文件路径
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
            
        Returns:
            (输出文件路径, 物流统计信息)
        """
        # 读取Excel文件，添加重试机制
        for attempt in range(max_retries):
            try:
                df = pd.read_excel(file_path)
                break
            except PermissionError:
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                else:
                    raise Exception('文件持续被占用，请确保Excel文件未被其他程序打开')
        
        # 统计物流单号
        from .statistics import StatisticsEngine
        stats_engine = StatisticsEngine()
        logistics_stats = stats_engine.analyze_logistics_numbers(df)
        
        # 创建数据透视表
        pivot = pd.pivot_table(
            df,
            index=['货主'],
            columns=['分类'],
            values=['货品数量'],
            aggfunc='sum',
            margins=True,
            margins_name='总计'
        )
        
        # 格式化数值
        for col in pivot.columns:
            pivot[col] = pivot[col].map(lambda x: int(round(x)) if pd.notnull(x) else x)
        
        # 生成输出文件名
        timestamp = datetime.now().strftime('%Y-%m-%d')
        base_name = os.path.splitext(os.path.basename(file_path))[0]
        output_dir = os.path.dirname(file_path)
        output_file = os.path.join(output_dir, f"{timestamp}线上出货数据汇总.xlsx")
        
        # 导出到Excel并美化
        self._export_pivot_to_excel(pivot, output_file)
        
        return output_file, logistics_stats
    
    def _export_to_excel(self, df: pd.DataFrame, file_path: str):
        """导出DataFrame到Excel"""
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 导出到Excel
        df.to_excel(file_path, index=False, engine='openpyxl')
    
    def _export_pivot_to_excel(self, pivot: pd.DataFrame, file_path: str):
        """导出数据透视表到Excel并美化格式"""
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "出货数据汇总"
        
        # 写入数据
        for r in dataframe_to_rows(pivot, index=True, header=True):
            ws.append(r)
        
        # 美化格式
        self._format_excel_sheet(ws)
        
        # 保存文件
        wb.save(file_path)
        wb.close()
    
    def _format_excel_sheet(self, ws):
        """美化Excel工作表格式"""
        # 定义样式
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        center_alignment = Alignment(horizontal='center', vertical='center')
        
        # 应用样式到表头
        for row in ws.iter_rows(min_row=1, max_row=2):
            for cell in row:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = center_alignment
                cell.border = border
        
        # 应用边框到所有单元格
        for row in ws.iter_rows():
            for cell in row:
                cell.border = border
                if cell.row > 2:  # 数据行居中对齐
                    cell.alignment = center_alignment
        
        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 20)
            ws.column_dimensions[column_letter].width = adjusted_width

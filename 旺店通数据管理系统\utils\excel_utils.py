"""
Excel工具函数
"""
import pandas as pd
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
import os
from typing import List, Dict, Any, Optional
from config import file_config

class ExcelUtils:
    """Excel工具类"""
    
    @staticmethod
    def validate_excel_file(file_path: str) -> Dict[str, Any]:
        """
        验证Excel文件
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            验证结果字典
        """
        result = {
            'valid': False,
            'message': '',
            'columns': [],
            'row_count': 0,
            'missing_columns': []
        }
        
        try:
            if not os.path.exists(file_path):
                result['message'] = '文件不存在'
                return result
            
            if not file_path.lower().endswith(('.xlsx', '.xls')):
                result['message'] = '不是有效的Excel文件'
                return result
            
            # 读取Excel文件
            df = pd.read_excel(file_path)
            
            result['columns'] = df.columns.tolist()
            result['row_count'] = len(df)
            
            # 检查必需的列
            missing_columns = []
            for required_col in file_config.REQUIRED_COLUMNS:
                if required_col not in df.columns:
                    missing_columns.append(required_col)
            
            result['missing_columns'] = missing_columns
            
            if missing_columns:
                result['message'] = f'缺少必需的列: {", ".join(missing_columns)}'
            else:
                result['valid'] = True
                result['message'] = '文件验证通过'
            
        except Exception as e:
            result['message'] = f'文件读取失败: {str(e)}'
        
        return result
    
    @staticmethod
    def read_excel_with_retry(file_path: str, max_retries: int = 3, retry_delay: int = 2) -> pd.DataFrame:
        """
        带重试机制的Excel读取
        
        Args:
            file_path: Excel文件路径
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
            
        Returns:
            DataFrame
        """
        import time
        
        for attempt in range(max_retries):
            try:
                return pd.read_excel(file_path)
            except PermissionError:
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                else:
                    raise Exception('文件持续被占用，请确保Excel文件未被其他程序打开')
            except Exception as e:
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                else:
                    raise e
    
    @staticmethod
    def format_excel_workbook(workbook_path: str) -> bool:
        """
        格式化Excel工作簿
        
        Args:
            workbook_path: 工作簿路径
            
        Returns:
            是否成功
        """
        try:
            wb = openpyxl.load_workbook(workbook_path)
            
            for sheet_name in wb.sheetnames:
                ws = wb[sheet_name]
                ExcelUtils._format_worksheet(ws)
            
            wb.save(workbook_path)
            wb.close()
            return True
            
        except Exception as e:
            print(f"格式化Excel失败: {e}")
            return False
    
    @staticmethod
    def _format_worksheet(ws):
        """格式化工作表"""
        # 定义样式
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        center_alignment = Alignment(horizontal='center', vertical='center')
        
        # 应用样式到表头（假设第一行是表头）
        if ws.max_row > 0:
            for cell in ws[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = center_alignment
                cell.border = border
        
        # 应用边框到所有单元格
        for row in ws.iter_rows():
            for cell in row:
                cell.border = border
                if cell.row > 1:  # 数据行居中对齐
                    cell.alignment = center_alignment
        
        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 20)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    @staticmethod
    def merge_excel_files(file_paths: List[str], output_path: str, sheet_name: str = "合并数据") -> bool:
        """
        合并多个Excel文件
        
        Args:
            file_paths: Excel文件路径列表
            output_path: 输出文件路径
            sheet_name: 工作表名称
            
        Returns:
            是否成功
        """
        try:
            all_data = []
            
            for file_path in file_paths:
                if os.path.exists(file_path):
                    df = pd.read_excel(file_path)
                    # 添加来源文件列
                    df['来源文件'] = os.path.basename(file_path)
                    all_data.append(df)
            
            if not all_data:
                return False
            
            # 合并数据
            merged_df = pd.concat(all_data, ignore_index=True)
            
            # 保存到Excel
            merged_df.to_excel(output_path, sheet_name=sheet_name, index=False)
            
            # 格式化
            ExcelUtils.format_excel_workbook(output_path)
            
            return True
            
        except Exception as e:
            print(f"合并Excel文件失败: {e}")
            return False
    
    @staticmethod
    def split_excel_by_column(file_path: str, column_name: str, output_dir: str) -> List[str]:
        """
        按列值拆分Excel文件
        
        Args:
            file_path: 源Excel文件路径
            column_name: 拆分依据的列名
            output_dir: 输出目录
            
        Returns:
            生成的文件路径列表
        """
        try:
            df = pd.read_excel(file_path)
            
            if column_name not in df.columns:
                return []
            
            os.makedirs(output_dir, exist_ok=True)
            generated_files = []
            
            # 按列值分组
            for value, group_df in df.groupby(column_name):
                # 生成文件名
                safe_value = str(value).replace('/', '_').replace('\\', '_')
                output_file = os.path.join(output_dir, f"{safe_value}.xlsx")
                
                # 保存分组数据
                group_df.to_excel(output_file, index=False)
                generated_files.append(output_file)
            
            return generated_files
            
        except Exception as e:
            print(f"拆分Excel文件失败: {e}")
            return []
    
    @staticmethod
    def create_summary_sheet(file_path: str, summary_data: Dict[str, Any]) -> bool:
        """
        创建汇总工作表
        
        Args:
            file_path: Excel文件路径
            summary_data: 汇总数据
            
        Returns:
            是否成功
        """
        try:
            # 读取现有工作簿或创建新的
            if os.path.exists(file_path):
                wb = openpyxl.load_workbook(file_path)
            else:
                wb = openpyxl.Workbook()
                # 删除默认工作表
                if 'Sheet' in wb.sheetnames:
                    wb.remove(wb['Sheet'])
            
            # 创建汇总工作表
            if '汇总' in wb.sheetnames:
                wb.remove(wb['汇总'])
            
            ws = wb.create_sheet('汇总', 0)  # 插入到第一个位置
            
            # 写入汇总数据
            row = 1
            for key, value in summary_data.items():
                ws.cell(row=row, column=1, value=key)
                ws.cell(row=row, column=2, value=str(value))
                row += 1
            
            # 格式化汇总表
            ExcelUtils._format_worksheet(ws)
            
            wb.save(file_path)
            wb.close()
            return True
            
        except Exception as e:
            print(f"创建汇总工作表失败: {e}")
            return False
    
    @staticmethod
    def get_excel_info(file_path: str) -> Dict[str, Any]:
        """
        获取Excel文件信息
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            文件信息字典
        """
        info = {
            'exists': False,
            'sheets': [],
            'total_rows': 0,
            'total_columns': 0,
            'file_size': 0,
            'modified_time': None
        }
        
        try:
            if not os.path.exists(file_path):
                return info
            
            info['exists'] = True
            info['file_size'] = os.path.getsize(file_path)
            info['modified_time'] = os.path.getmtime(file_path)
            
            wb = openpyxl.load_workbook(file_path, read_only=True)
            
            for sheet_name in wb.sheetnames:
                ws = wb[sheet_name]
                sheet_info = {
                    'name': sheet_name,
                    'rows': ws.max_row,
                    'columns': ws.max_column
                }
                info['sheets'].append(sheet_info)
                info['total_rows'] += ws.max_row
                info['total_columns'] = max(info['total_columns'], ws.max_column)
            
            wb.close()
            
        except Exception as e:
            info['error'] = str(e)
        
        return info

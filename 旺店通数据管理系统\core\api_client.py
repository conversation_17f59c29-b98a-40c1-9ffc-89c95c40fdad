"""
旺店通API客户端模块
"""
import hashlib
import json
import logging
import time
from typing import Dict, Any, Optional
import requests
from config import wdt_config

class WDTPostSignature:
    """旺店通POST API签名工具"""
    
    @staticmethod
    def build_request_params(
        method: str,
        sid: str,
        appkey: str,
        app_secret: str,
        compress_response_body: bool = True
    ) -> Dict[str, Any]:
        """构建公共请求参数"""
        params = {
            'method': method,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'format': 'json',
            'appkey': appkey,
            'sign_method': 'md5',
            'sid': sid
        }

        if compress_response_body:
            params['compress_response_body'] = '1'

        return params

    @staticmethod
    def calculate_signature(
        public_params: Dict[str, Any],
        request_body: str,
        app_secret: str
    ) -> str:
        """计算签名"""
        # 获取secret和salt
        secret, salt = wdt_config.get_secret_and_salt()
        
        # 构建签名字符串
        sorted_params = sorted(public_params.items())
        param_string = ''.join([f"{k}{v}" for k, v in sorted_params])
        
        # 构建完整的签名字符串
        sign_string = f"{secret}{param_string}{request_body}{secret}"
        
        # 如果有salt，添加到末尾
        if salt:
            sign_string += salt
        
        # 计算MD5
        return hashlib.md5(sign_string.encode('utf-8')).hexdigest().upper()

class WDTPostAPIException(Exception):
    """旺店通API异常"""
    pass

class WDTAPIClient:
    """旺店通WMS API客户端"""
    
    def __init__(self):
        """初始化客户端"""
        self.config = wdt_config
        self.logger = logging.getLogger(__name__)
        self.timeout = self.config.REQUEST_TIMEOUT
        self.base_url = self.config.API_URL
        
    def call_api(self, method: str, business_params: Dict[str, Any], compress_response: bool = True) -> Dict[str, Any]:
        """
        调用API
        
        Args:
            method: 接口方法名
            business_params: 业务参数
            compress_response: 是否启用响应压缩
            
        Returns:
            API响应数据
        """
        try:
            # 构建公共参数
            public_params = WDTPostSignature.build_request_params(
                method=method,
                sid=self.config.SID,
                appkey=self.config.APP_KEY,
                app_secret=self.config.APP_SECRET,
                compress_response_body=compress_response
            )
            
            # 构建请求体
            request_body = json.dumps(business_params, ensure_ascii=False, separators=(',', ':'))
            
            # 计算签名
            signature = WDTPostSignature.calculate_signature(
                public_params=public_params,
                request_body=request_body,
                app_secret=self.config.APP_SECRET
            )
            
            # 添加签名到公共参数
            public_params['sign'] = signature
            
            # 构建URL
            url_params = []
            for key, value in public_params.items():
                url_params.append(f"{key}={value}")
            
            full_url = f"{self.base_url}?{'&'.join(url_params)}"
            
            # 发送POST请求
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'WDT-POST-Client/1.0'
            }
            
            response = requests.post(
                full_url,
                data=request_body,
                headers=headers,
                timeout=self.timeout
            )
            
            # 检查HTTP状态
            if response.status_code != 200:
                raise WDTPostAPIException(f"HTTP请求失败: {response.status_code}")
            
            # 解析响应
            try:
                result = response.json()
            except json.JSONDecodeError as e:
                raise WDTPostAPIException(f"响应JSON解析失败: {e}")
            
            # 检查API响应状态
            if result.get('status') != 0:
                error_msg = result.get('message', '未知错误')
                raise WDTPostAPIException(f"API调用失败: {error_msg}")
            
            return result.get('data', {})
            
        except requests.RequestException as e:
            raise WDTPostAPIException(f"网络请求失败: {e}")
        except Exception as e:
            raise WDTPostAPIException(f"API调用异常: {e}")

    def query_stockout(
        self,
        start_consign_time: str,
        end_consign_time: str,
        page_no: int = 0,
        page_size: int = 100,
        warehouse_no: Optional[str] = None,
        owner_no: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        查询出库单

        Args:
            start_consign_time: 开始发货时间
            end_consign_time: 结束发货时间
            page_no: 页号，从0开始
            page_size: 分页大小，最大100
            warehouse_no: 仓库编号（可选）
            owner_no: 货主编号（可选）

        Returns:
            查询结果
        """
        params = {
            'start_consign_time': start_consign_time,
            'end_consign_time': end_consign_time,
            'page_no': page_no,
            'page_size': page_size
        }

        if warehouse_no:
            params['warehouse_no'] = warehouse_no
        if owner_no:
            params['owner_no'] = owner_no

        return self.call_api('stockout.query', params)

    def fetch_all_stockout_data(
        self,
        start_consign_time: str,
        end_consign_time: str,
        warehouse_no: Optional[str] = None,
        owner_no: Optional[str] = None,
        progress_callback: Optional[callable] = None
    ) -> list:
        """
        获取所有出库数据（分页获取）

        Args:
            start_consign_time: 开始发货时间
            end_consign_time: 结束发货时间
            warehouse_no: 仓库编号（可选）
            owner_no: 货主编号（可选）
            progress_callback: 进度回调函数

        Returns:
            所有出库数据列表
        """
        all_data = []
        page_no = 0
        page_size = self.config.DEFAULT_PAGE_SIZE

        while True:
            try:
                response = self.query_stockout(
                    start_consign_time=start_consign_time,
                    end_consign_time=end_consign_time,
                    page_no=page_no,
                    page_size=page_size,
                    warehouse_no=warehouse_no,
                    owner_no=owner_no
                )

                # 获取当前页数据
                current_data = response.get('order', [])
                if not current_data:
                    break

                all_data.extend(current_data)

                # 调用进度回调
                if progress_callback:
                    progress_callback(f"已获取 {len(all_data)} 条记录...")

                # 检查是否还有更多数据
                total_count = response.get('total_count', 0)
                if len(all_data) >= total_count:
                    break

                page_no += 1

                # 添加延迟避免频率限制
                time.sleep(self.config.BATCH_DELAY)

            except Exception as e:
                self.logger.error(f"获取第{page_no}页数据失败: {e}")
                break

        return all_data

    def test_connection(self) -> bool:
        """测试连接"""
        try:
            from datetime import datetime
            
            # 使用简单查询测试连接
            response = self.query_stockout(
                start_consign_time=datetime.now().strftime('%Y-%m-%d 00:00:00'),
                end_consign_time=datetime.now().strftime('%Y-%m-%d 23:59:59'),
                page_size=1,
                page_no=0
            )
            return True
        except Exception as e:
            self.logger.error(f"连接测试失败: {e}")
            return False

"""
旺店通数据管理系统 - 配置文件
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class WDTConfig:
    """旺店通WMS API配置类"""
    
    # API基本配置
    SID = os.getenv('WDT_SID', 'changhe')
    APP_KEY = os.getenv('WDT_APP_KEY', 'changhe_chycchsjcjgj_wdt')
    APP_SECRET = os.getenv('WDT_APP_SECRET', '72a70a3bedc51497a2a3b9c229d7df69')
    API_URL = os.getenv('WDT_API_URL', 'https://openapi.wdtwms.com/open_api/service.php')

    @classmethod
    def get_secret_and_salt(cls):
        """
        解析APP_SECRET获取secret和salt
        如果APP_SECRET包含冒号，则冒号前为secret，冒号后为salt
        否则整个APP_SECRET作为secret，salt为空
        """
        if ':' in cls.APP_SECRET:
            secret, salt = cls.APP_SECRET.split(':', 1)
            return secret, salt
        else:
            return cls.APP_SECRET, ''
    
    # 默认配置
    DEFAULT_PAGE_SIZE = int(os.getenv('DEFAULT_PAGE_SIZE', '100'))
    DEFAULT_WAREHOUSE_NO = os.getenv('DEFAULT_WAREHOUSE_NO', '')
    DEFAULT_SHOP_NOS = os.getenv('DEFAULT_SHOP_NOS', '')
    
    # API版本
    API_VERSION = '1.0'
    
    # 请求超时时间（秒）
    REQUEST_TIMEOUT = 30

    # 重试次数
    MAX_RETRIES = 3

    # 频率限制配置
    MAX_QPS = int(os.getenv('WDT_MAX_QPS', '10'))  # 每秒最大请求数
    RATE_LIMIT_WINDOW = 1  # 时间窗口（秒）

    # 批量请求配置
    BATCH_SIZE = int(os.getenv('WDT_BATCH_SIZE', '100'))  # 批量请求大小
    BATCH_DELAY = float(os.getenv('WDT_BATCH_DELAY', '0.1'))  # 批量请求间隔（秒）

class AppConfig:
    """应用程序配置类"""
    
    # 应用信息
    APP_NAME = "旺店通数据管理系统"
    APP_VERSION = "1.0.0"
    
    # 默认路径配置
    DEFAULT_OUTPUT_PATH = "C:/Users/<USER>/OneDrive/Desktop/旺店通出货数据/旺店通出库数据.xlsx"
    DEFAULT_ARCHIVE_PATH = "货品档案.xlsx"
    DEFAULT_MONTHLY_REPORT_PATH = "月度报告"
    
    # GUI配置
    WINDOW_WIDTH = 1000
    WINDOW_HEIGHT = 800
    LOG_MAX_LINES = 1000
    
    # 定时任务配置
    DEFAULT_INTERVAL_MINUTES = 60
    DEFAULT_DAILY_TIMES = ["09:00", "14:00", "18:00"]
    
    # 文件监控配置
    MONITOR_INTERVAL = 2  # 秒
    FILE_RETRY_DELAY = 2  # 秒
    FILE_MAX_RETRIES = 3
    
    # 微信发送配置
    WECHAT_SEND_TIMEOUT = 15  # 秒
    WECHAT_RETRY_COUNT = 3
    
    # 月度统计配置
    MONTHLY_CHART_WIDTH = 12
    MONTHLY_CHART_HEIGHT = 8
    MONTHLY_DPI = 300
    
    # 支持的图表类型
    CHART_TYPES = {
        'bar': '柱状图',
        'line': '折线图',
        'pie': '饼图',
        'area': '面积图'
    }
    
    # 统计维度
    STATISTICS_DIMENSIONS = {
        'owner': '货主',
        'category': '分类',
        'logistics': '物流公司',
        'daily': '日期'
    }

class FileConfig:
    """文件配置类"""
    
    # 支持的文件格式
    EXCEL_EXTENSIONS = ['.xlsx', '.xls']
    IMAGE_EXTENSIONS = ['.png', '.jpg', '.jpeg']
    
    # Excel列名映射
    COLUMN_MAPPING = {
        'owner_name': '货主',
        'goods_no': '货品编号',
        'logistics_no': '物流单号',
        'goods_num': '货品数量',
        'goods_remark': '分类',
        'consign_time': '发货时间'
    }
    
    # 必需的Excel列
    REQUIRED_COLUMNS = ['货主', '物流单号', '货品数量', '分类']
    
    # 物流公司识别规则
    LOGISTICS_RULES = {
        'SF': '顺丰快递',
        'JD': '京东快递',
        'YT': '圆通快递',
        'ZT': '中通快递',
        'ST': '申通快递',
        'YD': '韵达快递',
        'EMS': 'EMS'
    }

class LogConfig:
    """日志配置类"""
    
    # 日志级别
    LOG_LEVELS = {
        'DEBUG': 10,
        'INFO': 20,
        'WARNING': 30,
        'ERROR': 40,
        'CRITICAL': 50
    }
    
    # 日志格式
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    DATE_FORMAT = '%Y-%m-%d %H:%M:%S'
    
    # 日志文件配置
    LOG_FILE = 'logs/wdt_system.log'
    LOG_MAX_SIZE = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT = 5
    
    # GUI日志颜色配置
    LOG_COLORS = {
        'info': '#000000',     # 黑色
        'success': '#008800',  # 绿色
        'error': '#CC0000',    # 红色
        'warning': '#FF8800'   # 橙色
    }

# 全局配置实例
wdt_config = WDTConfig()
app_config = AppConfig()
file_config = FileConfig()
log_config = LogConfig()

# 配置保存和自动启动功能说明

## 🎯 新增功能概述

为旺店通数据自动获取GUI工具添加了两个重要功能：
1. **配置自动保存**: 程序自动记住用户的所有设置
2. **自动启动定时任务**: 程序启动时自动开始定时任务

## ✅ 功能详情

### 1. 配置自动保存

#### 保存的配置项
- **输出文件路径**: Excel文件保存位置
- **货品档案路径**: 货品档案Excel文件位置
- **定时模式**: 间隔执行或每日定时
- **间隔时间**: 间隔执行的分钟数
- **每日执行时间**: 所有设置的每日执行时间点
- **自动启动设置**: 是否启用自动启动
- **时间选择器状态**: 当前选择的小时和分钟

#### 自动保存时机
- **路径变更时**: 选择新的输出路径或货品档案路径
- **时间管理时**: 添加或删除每日执行时间
- **设置变更时**: 修改自动启动选项
- **程序关闭时**: 保存所有当前设置

#### 配置文件
- **文件名**: `wdt_gui_config.json`
- **位置**: 程序同目录
- **格式**: JSON格式，人类可读
- **编码**: UTF-8，支持中文路径

### 2. 自动启动定时任务

#### 启动条件
- **配置完整**: 输出路径和货品档案路径已设置
- **文件存在**: 货品档案文件确实存在
- **时间设置**: 每日定时模式下至少有一个执行时间
- **选项启用**: "程序启动时自动开始定时任务"已勾选

#### 启动流程
```
程序启动
    ↓
加载配置文件
    ↓
检查自动启动选项
    ↓
延迟3秒（显示启动信息）
    ↓
验证配置完整性
    ↓
自动启动定时任务
    ↓
显示成功信息
```

#### 安全机制
- **配置验证**: 启动前验证所有必要配置
- **错误处理**: 配置不完整时显示具体错误信息
- **延迟启动**: 3秒延迟确保界面完全加载
- **日志记录**: 详细记录启动过程和结果

## 🎛️ 界面更新

### 新增控件

#### 自动启动选项
```
☑ 程序启动时自动开始定时任务  [保存配置]
```
- **复选框**: 控制是否自动启动
- **保存按钮**: 手动保存当前配置
- **实时保存**: 选项变化时自动保存

#### 状态显示增强
```
[程序启动完成]
[配置文件: wdt_gui_config.json]
[输出路径: C:/Users/<USER>/旺店通出库数据.xlsx]
[货品档案: 货品档案.xlsx]
[每日执行时间: 09:00, 14:00, 18:00]
[⏰ 自动启动已启用，3秒后开始定时任务...]
[🚀 自动启动成功！]
```

## 📁 配置文件示例

### wdt_gui_config.json
```json
{
  "output_path": "C:/Users/<USER>/OneDrive/Desktop/旺店通出货数据/旺店通出库数据.xlsx",
  "archive_path": "货品档案.xlsx",
  "interval_minutes": 60,
  "auto_start": true,
  "timer_mode": "daily",
  "daily_hour": 9,
  "daily_minute": 0,
  "daily_times": [
    "09:00",
    "14:00",
    "18:00"
  ],
  "last_saved": "2025-07-14 15:30:00"
}
```

### 配置项说明
| 字段 | 类型 | 说明 | 默认值 |
|------|------|------|--------|
| output_path | string | 输出Excel文件路径 | 默认路径 |
| archive_path | string | 货品档案文件路径 | "货品档案.xlsx" |
| interval_minutes | number | 间隔执行分钟数 | 60 |
| auto_start | boolean | 是否自动启动 | true |
| timer_mode | string | 定时模式 | "daily" |
| daily_hour | number | 时间选择器-小时 | 9 |
| daily_minute | number | 时间选择器-分钟 | 0 |
| daily_times | array | 每日执行时间列表 | ["09:00", "14:00", "18:00"] |
| last_saved | string | 最后保存时间 | 当前时间 |

## 🚀 使用方法

### 首次使用
1. **启动程序**: 运行 `python wdt_data_gui.py`
2. **自动配置**: 程序使用默认配置并自动启动
3. **调整设置**: 根据需要修改路径和时间设置
4. **自动保存**: 所有修改会自动保存

### 日常使用
1. **双击启动**: 程序自动加载上次配置
2. **自动运行**: 如果启用自动启动，程序会自动开始定时任务
3. **无需配置**: 所有设置都已保存，无需重新配置

### 配置管理
1. **修改设置**: 任何设置修改都会自动保存
2. **手动保存**: 点击"保存配置"按钮强制保存
3. **重置配置**: 删除配置文件可恢复默认设置

## 📊 启动日志示例

### 成功启动
```
[2025-07-14 15:30:00] 程序启动完成
[2025-07-14 15:30:01] 配置文件: wdt_gui_config.json
[2025-07-14 15:30:02] 输出路径: C:/Users/<USER>/旺店通出库数据.xlsx
[2025-07-14 15:30:03] 货品档案: 货品档案.xlsx
[2025-07-14 15:30:04] 每日执行时间: 09:00, 14:00, 18:00
[2025-07-14 15:30:05] ⏰ 自动启动已启用，3秒后开始定时任务...
[2025-07-14 15:30:08] 定时任务已启动 - 每日定时 (09:00, 14:00, 18:00)
[2025-07-14 15:30:09] 🚀 自动启动成功！
```

### 配置不完整
```
[2025-07-14 15:30:00] 程序启动完成
[2025-07-14 15:30:01] 配置文件: wdt_gui_config.json
[2025-07-14 15:30:02] 输出路径: 
[2025-07-14 15:30:03] 货品档案: 货品档案.xlsx
[2025-07-14 15:30:04] ⏰ 自动启动已启用，3秒后开始定时任务...
[2025-07-14 15:30:07] ❌ 自动启动失败: 未设置输出文件路径
```

### 文件不存在
```
[2025-07-14 15:30:00] 程序启动完成
[2025-07-14 15:30:01] 配置文件: wdt_gui_config.json
[2025-07-14 15:30:02] 输出路径: C:/Users/<USER>/旺店通出库数据.xlsx
[2025-07-14 15:30:03] 货品档案: 不存在的文件.xlsx
[2025-07-14 15:30:04] ⏰ 自动启动已启用，3秒后开始定时任务...
[2025-07-14 15:30:07] ❌ 自动启动失败: 货品档案文件不存在 - 不存在的文件.xlsx
```

## 🔧 高级功能

### 1. 配置备份
- 配置文件是标准JSON格式
- 可以手动备份和恢复
- 支持在不同电脑间迁移配置

### 2. 配置重置
```bash
# 删除配置文件恢复默认设置
del wdt_gui_config.json
```

### 3. 配置编辑
- 可以直接编辑JSON文件
- 修改后重启程序生效
- 支持批量配置多台电脑

### 4. 错误恢复
- 配置文件损坏时自动使用默认配置
- 显示详细错误信息
- 不影响程序正常运行

## ⚠️ 注意事项

### 1. 文件权限
- 确保程序目录有写入权限
- 配置文件需要读写权限
- 避免在只读目录运行程序

### 2. 路径有效性
- 输出路径的目录必须存在
- 货品档案文件必须存在且可读
- 路径中的中文字符已正确支持

### 3. 自动启动
- 自动启动前会验证所有配置
- 配置不完整时不会启动
- 可以随时手动启动定时任务

### 4. 配置同步
- 多个程序实例会共享同一配置文件
- 最后关闭的程序会保存最终配置
- 建议避免同时运行多个实例

## 🎉 使用场景

### 1. 开发测试
- 频繁修改配置时自动保存
- 重启程序时保持设置
- 快速切换不同配置

### 2. 生产环境
- 一次配置，长期使用
- 自动启动，无人值守
- 稳定可靠的定时执行

### 3. 多用户环境
- 每个用户独立配置
- 配置文件便于管理
- 支持配置标准化

### 4. 系统集成
- 配置文件便于自动化部署
- 支持批量配置管理
- 易于监控和维护

## 📈 优势总结

### 1. 用户体验
- **零配置启动**: 首次使用即可自动运行
- **设置记忆**: 永久保存用户偏好
- **自动化**: 减少重复操作

### 2. 可靠性
- **配置验证**: 启动前检查配置完整性
- **错误处理**: 友好的错误提示
- **安全启动**: 多重验证确保稳定运行

### 3. 维护性
- **标准格式**: JSON配置文件易于理解
- **详细日志**: 完整记录操作过程
- **版本兼容**: 向后兼容旧版配置

### 4. 扩展性
- **灵活配置**: 易于添加新的配置项
- **模块化**: 配置管理独立模块
- **可定制**: 支持个性化设置

---

**功能完成时间**: 2025-07-14 15:35  
**版本**: v2.1  
**状态**: 已完成并测试通过 ✅  
**新增功能**: 配置保存 + 自动启动 🎯

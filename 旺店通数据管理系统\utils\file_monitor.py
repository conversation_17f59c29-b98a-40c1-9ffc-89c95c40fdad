"""
文件监控器
"""
import os
import time
import threading
from datetime import datetime
from typing import Optional, Callable
from config import app_config

class FileMonitor:
    """文件监控器"""
    
    def __init__(self, file_path: str, callback: Optional[Callable] = None):
        self.file_path = file_path
        self.callback = callback
        self.monitoring = False
        self.monitor_thread = None
        self.last_modified_time = None
        self.interval = app_config.MONITOR_INTERVAL
    
    def start_monitoring(self) -> bool:
        """开始监控"""
        if self.monitoring:
            return False
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        return True
    
    def stop_monitoring(self) -> bool:
        """停止监控"""
        if not self.monitoring:
            return False
        
        self.monitoring = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        return True
    
    def is_monitoring(self) -> bool:
        """检查是否正在监控"""
        return self.monitoring
    
    def set_file_path(self, file_path: str) -> None:
        """设置监控文件路径"""
        self.file_path = file_path
        self.last_modified_time = None
    
    def set_callback(self, callback: Callable) -> None:
        """设置回调函数"""
        self.callback = callback
    
    def _monitor_loop(self) -> None:
        """监控循环"""
        while self.monitoring:
            try:
                current_file_path = self.file_path
                
                if os.path.exists(current_file_path):
                    # 获取文件修改时间
                    current_modified_time = os.path.getmtime(current_file_path)
                    
                    # 检查文件是否被修改
                    if self.last_modified_time is not None and current_modified_time > self.last_modified_time:
                        if self.callback:
                            try:
                                self.callback(current_file_path)
                            except Exception as e:
                                print(f"回调函数执行失败: {e}")
                    
                    self.last_modified_time = current_modified_time
                
            except Exception as e:
                print(f"文件监控出错: {e}")
            
            time.sleep(self.interval)
    
    def get_file_info(self) -> dict:
        """获取文件信息"""
        if not os.path.exists(self.file_path):
            return {
                'exists': False,
                'size': 0,
                'modified_time': None,
                'status': '文件不存在'
            }
        
        try:
            stat = os.stat(self.file_path)
            return {
                'exists': True,
                'size': stat.st_size,
                'modified_time': datetime.fromtimestamp(stat.st_mtime),
                'status': '正常'
            }
        except Exception as e:
            return {
                'exists': True,
                'size': 0,
                'modified_time': None,
                'status': f'读取失败: {e}'
            }

class MultiFileMonitor:
    """多文件监控器"""
    
    def __init__(self):
        self.monitors = {}
        self.global_callback = None
    
    def add_file(self, file_id: str, file_path: str, callback: Optional[Callable] = None) -> bool:
        """添加监控文件"""
        if file_id in self.monitors:
            return False
        
        monitor = FileMonitor(file_path, callback or self.global_callback)
        self.monitors[file_id] = monitor
        return True
    
    def remove_file(self, file_id: str) -> bool:
        """移除监控文件"""
        if file_id not in self.monitors:
            return False
        
        monitor = self.monitors[file_id]
        monitor.stop_monitoring()
        del self.monitors[file_id]
        return True
    
    def start_all(self) -> int:
        """开始监控所有文件"""
        started_count = 0
        for monitor in self.monitors.values():
            if monitor.start_monitoring():
                started_count += 1
        return started_count
    
    def stop_all(self) -> int:
        """停止监控所有文件"""
        stopped_count = 0
        for monitor in self.monitors.values():
            if monitor.stop_monitoring():
                stopped_count += 1
        return stopped_count
    
    def start_file(self, file_id: str) -> bool:
        """开始监控指定文件"""
        if file_id not in self.monitors:
            return False
        return self.monitors[file_id].start_monitoring()
    
    def stop_file(self, file_id: str) -> bool:
        """停止监控指定文件"""
        if file_id not in self.monitors:
            return False
        return self.monitors[file_id].stop_monitoring()
    
    def set_global_callback(self, callback: Callable) -> None:
        """设置全局回调函数"""
        self.global_callback = callback
        for monitor in self.monitors.values():
            monitor.set_callback(callback)
    
    def get_status(self) -> dict:
        """获取所有文件的监控状态"""
        status = {}
        for file_id, monitor in self.monitors.items():
            status[file_id] = {
                'monitoring': monitor.is_monitoring(),
                'file_info': monitor.get_file_info()
            }
        return status
    
    def update_file_path(self, file_id: str, new_path: str) -> bool:
        """更新文件路径"""
        if file_id not in self.monitors:
            return False
        
        monitor = self.monitors[file_id]
        was_monitoring = monitor.is_monitoring()
        
        if was_monitoring:
            monitor.stop_monitoring()
        
        monitor.set_file_path(new_path)
        
        if was_monitoring:
            monitor.start_monitoring()
        
        return True

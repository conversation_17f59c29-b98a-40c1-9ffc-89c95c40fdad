"""
主窗口GUI
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
from datetime import datetime
from typing import Optional, Dict, Any
from config import app_config, log_config

class MainWindow:
    """主窗口类"""
    
    def __init__(self, root: tk.Tk, app_instance):
        self.root = root
        self.app = app_instance
        
        # 状态变量
        self.api_status = tk.StringVar(value="未启动")
        self.monitor_status = tk.StringVar(value="未启动")
        self.last_update_time = tk.StringVar(value="无")
        self.last_api_fetch = tk.StringVar(value="无")
        
        # 日志消息列表
        self.log_messages = []
        
        # 创建界面
        self._create_widgets()
        
        # 加载配置
        self._load_config()
    
    def _create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 创建各个区域
        self._create_status_area(main_frame)
        self._create_api_area(main_frame)
        self._create_monitor_area(main_frame)
        self._create_monthly_area(main_frame)
        self._create_log_area(main_frame)
        self._create_button_area(main_frame)
    
    def _create_status_area(self, parent):
        """创建状态区域"""
        status_frame = ttk.LabelFrame(parent, text="系统状态", padding="5")
        status_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # API状态
        ttk.Label(status_frame, text="API定时任务:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Label(status_frame, textvariable=self.api_status).grid(row=0, column=1, sticky=tk.W)
        
        # 监控状态
        ttk.Label(status_frame, text="文件监控:").grid(row=0, column=2, sticky=tk.W, padx=(20, 10))
        ttk.Label(status_frame, textvariable=self.monitor_status).grid(row=0, column=3, sticky=tk.W)
        
        # 最后更新时间
        ttk.Label(status_frame, text="最后处理:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Label(status_frame, textvariable=self.last_update_time).grid(row=1, column=1, sticky=tk.W)
        
        # 最后API获取时间
        ttk.Label(status_frame, text="最后API获取:").grid(row=1, column=2, sticky=tk.W, padx=(20, 10))
        ttk.Label(status_frame, textvariable=self.last_api_fetch).grid(row=1, column=3, sticky=tk.W)
    
    def _create_api_area(self, parent):
        """创建API区域"""
        api_frame = ttk.LabelFrame(parent, text="API数据获取", padding="5")
        api_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N), padx=(0, 5), pady=(0, 10))
        
        # 输出路径
        ttk.Label(api_frame, text="输出路径:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.output_path_var = tk.StringVar()
        output_entry = ttk.Entry(api_frame, textvariable=self.output_path_var, width=40)
        output_entry.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        ttk.Button(api_frame, text="浏览", command=self._browse_output_path).grid(row=1, column=1, padx=(5, 0), pady=(0, 5))
        
        # 货品档案路径
        ttk.Label(api_frame, text="货品档案:").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        self.archive_path_var = tk.StringVar()
        archive_entry = ttk.Entry(api_frame, textvariable=self.archive_path_var, width=40)
        archive_entry.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        ttk.Button(api_frame, text="浏览", command=self._browse_archive_path).grid(row=3, column=1, padx=(5, 0), pady=(0, 5))
        
        # 控制按钮
        button_frame = ttk.Frame(api_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=(10, 0))
        
        self.api_start_btn = ttk.Button(button_frame, text="启动定时任务", command=self._start_api_timer)
        self.api_start_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.api_stop_btn = ttk.Button(button_frame, text="停止定时任务", command=self._stop_api_timer, state=tk.DISABLED)
        self.api_stop_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(button_frame, text="立即获取", command=self._fetch_now).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="测试连接", command=self._test_api_connection).pack(side=tk.LEFT)
        
        api_frame.columnconfigure(0, weight=1)
    
    def _create_monitor_area(self, parent):
        """创建监控区域"""
        monitor_frame = ttk.LabelFrame(parent, text="文件监控与处理", padding="5")
        monitor_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N), padx=(5, 0), pady=(0, 10))
        
        # 监控文件路径
        ttk.Label(monitor_frame, text="监控文件:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.monitor_path_var = tk.StringVar()
        monitor_entry = ttk.Entry(monitor_frame, textvariable=self.monitor_path_var, width=40)
        monitor_entry.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        ttk.Button(monitor_frame, text="浏览", command=self._browse_monitor_path).grid(row=1, column=1, padx=(5, 0), pady=(0, 5))
        
        # 微信联系人
        ttk.Label(monitor_frame, text="微信联系人:").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        self.contacts_var = tk.StringVar()
        ttk.Entry(monitor_frame, textvariable=self.contacts_var, width=40).grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # 选项
        options_frame = ttk.Frame(monitor_frame)
        options_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))
        
        self.auto_process_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="自动处理", variable=self.auto_process_var).pack(side=tk.LEFT)
        
        self.auto_send_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="自动发送", variable=self.auto_send_var).pack(side=tk.LEFT, padx=(10, 0))
        
        self.send_as_image_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="发送图片", variable=self.send_as_image_var).pack(side=tk.LEFT, padx=(10, 0))
        
        # 控制按钮
        button_frame = ttk.Frame(monitor_frame)
        button_frame.grid(row=5, column=0, columnspan=2, pady=(10, 0))
        
        self.monitor_start_btn = ttk.Button(button_frame, text="开始监控", command=self._start_monitoring)
        self.monitor_start_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.monitor_stop_btn = ttk.Button(button_frame, text="停止监控", command=self._stop_monitoring, state=tk.DISABLED)
        self.monitor_stop_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(button_frame, text="立即处理", command=self._process_now).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="测试微信", command=self._test_wechat).pack(side=tk.LEFT)
        
        monitor_frame.columnconfigure(0, weight=1)
    
    def _create_monthly_area(self, parent):
        """创建月度统计区域"""
        monthly_frame = ttk.LabelFrame(parent, text="月度统计报告", padding="5")
        monthly_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 月度统计按钮
        ttk.Button(monthly_frame, text="生成月度报告", command=self._open_monthly_dialog).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(monthly_frame, text="查看历史报告", command=self._view_monthly_reports).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(monthly_frame, text="配置设置", command=self._open_config_dialog).pack(side=tk.LEFT)
    
    def _create_log_area(self, parent):
        """创建日志区域"""
        log_frame = ttk.LabelFrame(parent, text="运行日志", padding="5")
        log_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 创建文本框和滚动条
        text_frame = ttk.Frame(log_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        self.log_text = tk.Text(text_frame, height=15, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 配置文本颜色标签
        for level, color in log_config.LOG_COLORS.items():
            self.log_text.tag_configure(level, foreground=color)
        
        # 日志控制按钮
        log_button_frame = ttk.Frame(log_frame)
        log_button_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(log_button_frame, text="清空日志", command=self._clear_log).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(log_button_frame, text="保存日志", command=self._save_log).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(log_button_frame, text="刷新", command=self._refresh_status).pack(side=tk.RIGHT)
    
    def _create_button_area(self, parent):
        """创建底部按钮区域"""
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        ttk.Button(button_frame, text="关于", command=self._show_about).pack(side=tk.LEFT)
        ttk.Button(button_frame, text="退出", command=self.root.quit).pack(side=tk.RIGHT)
    
    def _load_config(self):
        """加载配置"""
        config = self.app.config_manager
        
        # 加载路径配置
        self.output_path_var.set(config.get('paths.output_path', ''))
        self.archive_path_var.set(config.get('paths.archive_path', ''))
        self.monitor_path_var.set(config.get('paths.output_path', ''))
        
        # 加载微信配置
        contacts = config.get('wechat.contacts', [])
        self.contacts_var.set(';'.join(contacts))
        
        # 加载选项配置
        self.auto_process_var.set(config.get('monitor.auto_process', True))
        self.auto_send_var.set(config.get('monitor.auto_send', True))
        self.send_as_image_var.set(config.get('wechat.send_as_image', True))
    
    def _save_config(self):
        """保存配置"""
        config = self.app.config_manager
        
        # 保存路径配置
        config.set('paths.output_path', self.output_path_var.get())
        config.set('paths.archive_path', self.archive_path_var.get())
        
        # 保存微信配置
        contacts_text = self.contacts_var.get().strip()
        contacts = [c.strip() for c in contacts_text.split(';') if c.strip()] if contacts_text else []
        config.set('wechat.contacts', contacts)
        
        # 保存选项配置
        config.set('monitor.auto_process', self.auto_process_var.get())
        config.set('monitor.auto_send', self.auto_send_var.get())
        config.set('wechat.send_as_image', self.send_as_image_var.get())
        
        config.save_config()
    
    # 事件处理方法
    def _browse_output_path(self):
        """浏览输出路径"""
        filename = filedialog.asksaveasfilename(
            title="选择输出Excel文件",
            defaultextension=".xlsx",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filename:
            self.output_path_var.set(filename)
            self._save_config()
    
    def _browse_archive_path(self):
        """浏览货品档案路径"""
        filename = filedialog.askopenfilename(
            title="选择货品档案Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
        )
        if filename:
            self.archive_path_var.set(filename)
            self._save_config()
    
    def _browse_monitor_path(self):
        """浏览监控文件路径"""
        filename = filedialog.askopenfilename(
            title="选择要监控的Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
        )
        if filename:
            self.monitor_path_var.set(filename)
            self._save_config()
    
    def _start_api_timer(self):
        """启动API定时任务"""
        self._save_config()
        if self.app.start_api_timer():
            self.api_status.set("运行中")
            self.api_start_btn.config(state=tk.DISABLED)
            self.api_stop_btn.config(state=tk.NORMAL)
            self.log_message("API定时任务已启动", "success")
        else:
            self.log_message("API定时任务启动失败", "error")
    
    def _stop_api_timer(self):
        """停止API定时任务"""
        self.app.stop_api_timer()
        self.api_status.set("已停止")
        self.api_start_btn.config(state=tk.NORMAL)
        self.api_stop_btn.config(state=tk.DISABLED)
        self.log_message("API定时任务已停止", "warning")
    
    def _start_monitoring(self):
        """开始文件监控"""
        self._save_config()
        if self.app.start_file_monitoring():
            self.monitor_status.set("监控中")
            self.monitor_start_btn.config(state=tk.DISABLED)
            self.monitor_stop_btn.config(state=tk.NORMAL)
            self.log_message("文件监控已启动", "success")
        else:
            self.log_message("文件监控启动失败", "error")
    
    def _stop_monitoring(self):
        """停止文件监控"""
        self.app.stop_file_monitoring()
        self.monitor_status.set("已停止")
        self.monitor_start_btn.config(state=tk.NORMAL)
        self.monitor_stop_btn.config(state=tk.DISABLED)
        self.log_message("文件监控已停止", "warning")
    
    def _fetch_now(self):
        """立即获取数据"""
        self.log_message("开始立即获取API数据...", "info")
        threading.Thread(target=self.app._execute_api_fetch, daemon=True).start()
    
    def _process_now(self):
        """立即处理数据"""
        file_path = self.monitor_path_var.get()
        if not file_path:
            messagebox.showerror("错误", "请先选择要处理的文件")
            return
        
        self.log_message("开始立即处理数据...", "info")
        threading.Thread(target=self.app._on_file_changed, args=(file_path,), daemon=True).start()
    
    def _test_api_connection(self):
        """测试API连接"""
        self.log_message("正在测试API连接...", "info")
        
        def test_thread():
            if self.app.api_client.test_connection():
                self.log_message("API连接测试成功", "success")
                messagebox.showinfo("测试结果", "API连接测试成功！")
            else:
                self.log_message("API连接测试失败", "error")
                messagebox.showerror("测试结果", "API连接测试失败！")
        
        threading.Thread(target=test_thread, daemon=True).start()
    
    def _test_wechat(self):
        """测试微信连接"""
        contacts_text = self.contacts_var.get().strip()
        if not contacts_text:
            messagebox.showerror("错误", "请先设置微信联系人")
            return
        
        contacts = [c.strip() for c in contacts_text.split(';') if c.strip()]
        if not contacts:
            messagebox.showerror("错误", "请输入有效的微信联系人")
            return
        
        contact = contacts[0]  # 测试第一个联系人
        self.log_message(f"正在测试微信连接: {contact}", "info")
        
        def test_thread():
            if self.app.wechat_sender.test_connection(contact):
                self.log_message("微信连接测试成功", "success")
                messagebox.showinfo("测试结果", f"微信连接测试成功！\n联系人: {contact}")
            else:
                self.log_message("微信连接测试失败", "error")
                messagebox.showerror("测试结果", f"微信连接测试失败！\n联系人: {contact}")
        
        threading.Thread(target=test_thread, daemon=True).start()
    
    def _open_monthly_dialog(self):
        """打开月度统计对话框"""
        from .monthly_dialog import MonthlyDialog
        dialog = MonthlyDialog(self.root, self.app)
        dialog.show()
    
    def _view_monthly_reports(self):
        """查看历史报告"""
        import os
        report_dir = self.app.config_manager.get('paths.monthly_report_path', '月度报告')
        if os.path.exists(report_dir):
            os.startfile(report_dir)
        else:
            messagebox.showinfo("提示", "暂无历史报告")
    
    def _open_config_dialog(self):
        """打开配置对话框"""
        from .config_dialog import ConfigDialog
        dialog = ConfigDialog(self.root, self.app.config_manager)
        dialog.show()
    
    def _clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log_messages.clear()
    
    def _save_log(self):
        """保存日志"""
        filename = filedialog.asksaveasfilename(
            title="保存日志文件",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                messagebox.showinfo("成功", "日志已保存")
            except Exception as e:
                messagebox.showerror("错误", f"保存日志失败: {e}")
    
    def _refresh_status(self):
        """刷新状态"""
        # 更新API状态
        if self.app.api_timer_running:
            self.api_status.set("运行中")
        else:
            self.api_status.set("已停止")
        
        # 更新监控状态
        if self.app.file_monitoring:
            self.monitor_status.set("监控中")
        else:
            self.monitor_status.set("已停止")
    
    def _show_about(self):
        """显示关于对话框"""
        about_text = f"""
{app_config.APP_NAME} v{app_config.APP_VERSION}

这是一个集成的旺店通数据管理系统，融合了：
• API数据获取
• 数据处理与统计
• 微信自动发送
• 月度报告生成

开发团队：Augment Agent
"""
        messagebox.showinfo("关于", about_text)
    
    # 回调方法（供主程序调用）
    def log_message(self, message: str, level: str = "info"):
        """添加日志消息"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        formatted_message = f"[{timestamp}] {message}\n"
        
        # 在主线程中更新UI
        self.root.after(0, self._update_log_text, formatted_message, level)
        
        # 保存到消息列表
        self.log_messages.append((timestamp, message, level))
        
        # 限制消息数量
        if len(self.log_messages) > app_config.LOG_MAX_LINES:
            self.log_messages = self.log_messages[-app_config.LOG_MAX_LINES:]
    
    def _update_log_text(self, message: str, level: str):
        """更新日志文本框"""
        self.log_text.insert(tk.END, message, level)
        self.log_text.see(tk.END)
    
    def on_api_fetch_completed(self, record_count: int):
        """API获取完成回调"""
        self.last_api_fetch.set(datetime.now().strftime('%H:%M:%S'))
        self.log_message(f"API数据获取完成，处理了 {record_count} 条记录", "success")
    
    def on_api_fetch_failed(self, error: str):
        """API获取失败回调"""
        self.log_message(f"API数据获取失败: {error}", "error")
    
    def on_file_processed(self, output_file: str, logistics_stats: dict):
        """文件处理完成回调"""
        self.last_update_time.set(datetime.now().strftime('%H:%M:%S'))
        self.log_message(f"文件处理完成: {output_file}", "success")
        if logistics_stats.get('message'):
            self.log_message(f"物流统计: {logistics_stats['message']}", "info")
    
    def on_file_process_failed(self, error: str):
        """文件处理失败回调"""
        self.log_message(f"文件处理失败: {error}", "error")

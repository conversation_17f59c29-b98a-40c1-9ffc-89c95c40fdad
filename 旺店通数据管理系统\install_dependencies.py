#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
旺店通数据管理系统依赖包安装脚本
"""

import subprocess
import sys
import os

def install_package(package_name, description=""):
    """安装单个包"""
    try:
        print(f"正在安装 {package_name}... {description}")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package_name, '--user'])
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        return False
    except Exception as e:
        print(f"❌ {package_name} 安装出错: {e}")
        return False

def main():
    """主安装函数"""
    print("=" * 60)
    print("旺店通数据管理系统 - 依赖包自动安装")
    print("=" * 60)
    
    # 核心依赖包（必需）
    core_packages = [
        ("pandas>=1.5.0", "数据处理核心库"),
        ("openpyxl>=3.0.0", "Excel文件处理"),
        ("numpy>=1.21.0", "数值计算库"),
        ("requests>=2.28.0", "HTTP请求库"),
        ("python-dotenv>=0.19.0", "环境变量管理"),
    ]
    
    # 可选依赖包
    optional_packages = [
        ("matplotlib>=3.5.0", "图表生成库"),
        ("seaborn>=0.11.0", "统计图表库"),
        ("pillow>=9.0.0", "图像处理库"),
        ("wxauto>=3.9.0", "微信自动化库"),
        ("sv-ttk>=2.0.0", "现代化GUI主题"),
        ("psutil>=5.9.0", "系统信息库"),
    ]
    
    # Windows特定包
    windows_packages = [
        ("pywin32>=304", "Windows系统API"),
    ]
    
    print("\n🔧 开始安装核心依赖包...")
    print("-" * 40)
    
    core_success = 0
    for package, desc in core_packages:
        if install_package(package, desc):
            core_success += 1
        print()
    
    print(f"\n核心依赖包安装完成: {core_success}/{len(core_packages)} 成功")
    
    if core_success < len(core_packages):
        print("⚠️ 部分核心依赖包安装失败，程序可能无法正常运行")
        response = input("是否继续安装可选依赖包? (y/n): ")
        if response.lower() != 'y':
            return
    
    print("\n🎨 开始安装可选依赖包...")
    print("-" * 40)
    
    optional_success = 0
    for package, desc in optional_packages:
        if install_package(package, desc):
            optional_success += 1
        print()
    
    print(f"\n可选依赖包安装完成: {optional_success}/{len(optional_packages)} 成功")
    
    # Windows特定包
    if os.name == 'nt':  # Windows系统
        print("\n🪟 开始安装Windows特定依赖包...")
        print("-" * 40)
        
        windows_success = 0
        for package, desc in windows_packages:
            if install_package(package, desc):
                windows_success += 1
            print()
        
        print(f"\nWindows依赖包安装完成: {windows_success}/{len(windows_packages)} 成功")
    
    print("\n" + "=" * 60)
    print("📋 安装总结")
    print("=" * 60)
    
    print(f"✅ 核心依赖包: {core_success}/{len(core_packages)} 成功")
    print(f"🎨 可选依赖包: {optional_success}/{len(optional_packages)} 成功")
    if os.name == 'nt':
        print(f"🪟 Windows依赖包: {windows_success}/{len(windows_packages)} 成功")
    
    print("\n📝 功能说明:")
    print("• pandas, openpyxl, numpy: 数据处理核心功能")
    print("• requests, python-dotenv: API数据获取功能")
    print("• matplotlib, seaborn: 图表生成功能")
    print("• pillow: 图像处理功能")
    print("• wxauto: 微信自动发送功能")
    print("• sv-ttk: 现代化界面主题")
    print("• pywin32: Windows系统集成")
    
    if core_success == len(core_packages):
        print("\n🎉 核心功能已就绪，可以运行程序了！")
        print("\n启动命令:")
        print("python main.py")
        
        if optional_success < len(optional_packages):
            print("\n⚠️ 部分可选功能可能不可用:")
            failed_packages = []
            for i, (package, desc) in enumerate(optional_packages):
                if i >= optional_success:
                    failed_packages.append(f"• {desc}")
            for failed in failed_packages:
                print(failed)
    else:
        print("\n❌ 核心依赖包安装不完整，请手动安装缺失的包")
        print("\n手动安装命令:")
        for package, desc in core_packages:
            print(f"pip install {package}")

if __name__ == "__main__":
    main()

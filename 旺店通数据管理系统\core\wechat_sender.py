"""
微信发送模块
"""
import os
import time
import logging
from typing import List, Optional, Callable, Dict
from config import app_config

# 检查微信功能可用性
try:
    import wxauto
    WECHAT_AVAILABLE = True
except ImportError:
    WECHAT_AVAILABLE = False

class WeChatSender:
    """微信发送器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.timeout = app_config.WECHAT_SEND_TIMEOUT
        self.retry_count = app_config.WECHAT_RETRY_COUNT
    
    def is_available(self) -> bool:
        """检查微信功能是否可用"""
        return WECHAT_AVAILABLE
    
    def send_message(self, contact_name: str, message: str) -> bool:
        """
        发送消息到微信
        
        Args:
            contact_name: 联系人名称
            message: 消息内容
            
        Returns:
            发送是否成功
        """
        if not WECHAT_AVAILABLE:
            self.logger.error("微信发送功能不可用")
            return False
        
        try:
            wx = wxauto.WeChat()
            wx.ChatWith(contact_name)
            time.sleep(1)  # 等待聊天窗口打开
            wx.SendMsg(message)
            
            self.logger.info(f"成功发送消息到微信联系人 '{contact_name}'")
            return True
            
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            return False
    
    def send_file(self, contact_name: str, file_path: str) -> bool:
        """
        发送文件到微信
        
        Args:
            contact_name: 联系人名称
            file_path: 文件路径
            
        Returns:
            发送是否成功
        """
        if not WECHAT_AVAILABLE:
            self.logger.error("微信发送功能不可用")
            return False
        
        if not os.path.exists(file_path):
            self.logger.error(f"文件不存在: {file_path}")
            return False
        
        try:
            wx = wxauto.WeChat()
            wx.ChatWith(contact_name)
            time.sleep(1)  # 等待聊天窗口打开
            wx.SendFiles(file_path)
            
            self.logger.info(f"成功发送文件到微信联系人 '{contact_name}': {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"发送文件失败: {e}")
            return False
    
    def send_to_multiple_contacts(
        self,
        contacts: List[str],
        message: Optional[str] = None,
        file_path: Optional[str] = None,
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, bool]:
        """
        发送到多个联系人
        
        Args:
            contacts: 联系人列表
            message: 消息内容（可选）
            file_path: 文件路径（可选）
            progress_callback: 进度回调函数
            
        Returns:
            发送结果字典 {联系人: 是否成功}
        """
        results = {}
        total_count = len(contacts)
        
        for i, contact in enumerate(contacts, 1):
            try:
                if progress_callback:
                    progress_callback(f"正在发送到联系人 {i}/{total_count}: {contact}")
                
                success = True
                
                # 发送消息
                if message:
                    msg_success = self.send_message(contact, message)
                    if not msg_success:
                        success = False
                        if progress_callback:
                            progress_callback(f"消息发送失败: {contact}")
                    else:
                        if progress_callback:
                            progress_callback(f"消息发送成功: {contact}")
                
                # 发送文件
                if file_path:
                    file_success = self.send_file(contact, file_path)
                    if not file_success:
                        success = False
                        if progress_callback:
                            progress_callback(f"文件发送失败: {contact}")
                    else:
                        if progress_callback:
                            progress_callback(f"文件发送成功: {contact}")
                
                results[contact] = success
                
                # 添加延迟避免发送过快
                if i < total_count:
                    time.sleep(1)
                    
            except Exception as e:
                self.logger.error(f"发送到 {contact} 时出错: {e}")
                results[contact] = False
                if progress_callback:
                    progress_callback(f"发送失败: {contact} - {e}")
        
        return results
    
    def test_connection(self, contact_name: str) -> bool:
        """
        测试微信连接
        
        Args:
            contact_name: 测试联系人名称
            
        Returns:
            连接是否正常
        """
        if not WECHAT_AVAILABLE:
            return False
        
        try:
            wx = wxauto.WeChat()
            wx.ChatWith(contact_name)
            return True
        except Exception as e:
            self.logger.error(f"微信连接测试失败: {e}")
            return False

class ExcelToImageConverter:
    """Excel转图片转换器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def is_available(self) -> bool:
        """检查转换功能是否可用"""
        try:
            import win32com.client
            from PIL import Image, ImageGrab
            return True
        except ImportError:
            return False
    
    def convert_simple(self, excel_file_path: str, output_image_path: Optional[str] = None) -> str:
        """
        简化的Excel转图片方法
        
        Args:
            excel_file_path: Excel文件路径
            output_image_path: 输出图片路径（可选）
            
        Returns:
            输出图片路径
        """
        if not self.is_available():
            raise Exception("Excel转图片功能不可用，缺少必要的依赖包")
        
        if not os.path.exists(excel_file_path):
            raise Exception(f"Excel文件不存在: {excel_file_path}")
        
        # 生成输出路径
        if not output_image_path:
            base_name = os.path.splitext(excel_file_path)[0]
            output_image_path = f"{base_name}_表格截图.png"
        
        try:
            import win32com.client
            from PIL import ImageGrab
            
            # 使用win32com打开Excel
            excel_app = win32com.client.Dispatch("Excel.Application")
            excel_app.Visible = False
            excel_app.DisplayAlerts = False
            
            # 打开工作簿
            workbook = excel_app.Workbooks.Open(os.path.abspath(excel_file_path))
            worksheet = workbook.ActiveSheet
            
            # 获取已使用的区域
            used_range = worksheet.UsedRange
            
            # 复制已使用区域为图片
            used_range.CopyPicture(1, 2)  # xlScreen, xlBitmap
            
            # 关闭工作簿
            workbook.Close(False)
            excel_app.Quit()
            
            # 使用PIL处理剪贴板中的图片
            img = ImageGrab.grabclipboard()
            
            if img:
                # 保存图片
                img.save(output_image_path, 'PNG', quality=95, dpi=(300, 300))
                return output_image_path
            else:
                raise Exception("无法从剪贴板获取图片")
                
        except Exception as e:
            raise Exception(f"Excel转图片失败: {str(e)}")
    
    def convert_advanced(self, excel_file_path: str, output_image_path: Optional[str] = None) -> str:
        """
        高级Excel转图片方法（使用pandas和matplotlib）
        
        Args:
            excel_file_path: Excel文件路径
            output_image_path: 输出图片路径（可选）
            
        Returns:
            输出图片路径
        """
        try:
            import pandas as pd
            import matplotlib.pyplot as plt
            from matplotlib import font_manager
            
            # 生成输出路径
            if not output_image_path:
                base_name = os.path.splitext(excel_file_path)[0]
                output_image_path = f"{base_name}_表格截图.png"
            
            # 读取Excel数据
            df = pd.read_excel(excel_file_path)
            
            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
            plt.rcParams['axes.unicode_minus'] = False
            
            # 创建图表
            fig, ax = plt.subplots(figsize=(12, 8))
            ax.axis('tight')
            ax.axis('off')
            
            # 创建表格
            table = ax.table(cellText=df.values, colLabels=df.columns, 
                           cellLoc='center', loc='center')
            
            # 设置表格样式
            table.auto_set_font_size(False)
            table.set_fontsize(10)
            table.scale(1.2, 1.5)
            
            # 保存图片
            plt.savefig(output_image_path, dpi=300, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            plt.close()
            
            return output_image_path
            
        except Exception as e:
            raise Exception(f"高级Excel转图片失败: {str(e)}")

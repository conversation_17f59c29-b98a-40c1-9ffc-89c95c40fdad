# -*- mode: python ; coding: utf-8 -*-

"""
旺店通数据自动获取工具 PyInstaller SPEC文件
用于打包GUI应用程序为独立可执行文件
"""

import os
import sys
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 获取当前目录
current_dir = os.path.dirname(os.path.abspath(SPEC))

# 分析主程序
a = Analysis(
    # 主程序入口
    ['wdt_data_gui.py'],
    
    # 搜索路径
    pathex=[current_dir],
    
    # 二进制文件
    binaries=[],
    
    # 数据文件
    datas=[
        # 配置文件
        ('config.py', '.'),
        # 示例配置文件（如果存在）
        ('wdt_gui_config.json', '.') if os.path.exists('wdt_gui_config.json') else None,
        # 货品档案示例文件（如果存在）
        ('货品档案.xlsx', '.') if os.path.exists('货品档案.xlsx') else None,
        # 说明文档
        ('GUI使用说明.md', 'docs') if os.path.exists('GUI使用说明.md') else None,
        ('GUI功能演示.md', 'docs') if os.path.exists('GUI功能演示.md') else None,
        ('每日定时功能说明.md', 'docs') if os.path.exists('每日定时功能说明.md') else None,
        ('物流单号过滤功能说明.md', 'docs') if os.path.exists('物流单号过滤功能说明.md') else None,
        ('配置保存和自动启动功能说明.md', 'docs') if os.path.exists('配置保存和自动启动功能说明.md') else None,
    ],
    
    # 隐藏导入的模块
    hiddenimports=[
        # 标准库
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'threading',
        'time',
        'os',
        'json',
        'hashlib',
        'logging',
        'datetime',
        'typing',
        
        # 第三方库
        'requests',
        'openpyxl',
        'openpyxl.workbook',
        'openpyxl.worksheet',
        'openpyxl.styles',
        'openpyxl.utils',
        'python-dotenv',
        'dotenv',
        
        # 项目模块
        'wdt_post_client',
        'export_to_wdt_data',
        'config',
        
        # openpyxl的子模块
        'openpyxl.xml',
        'openpyxl.xml.functions',
        'openpyxl.xml.constants',
        'openpyxl.packaging',
        'openpyxl.packaging.manifest',
        'openpyxl.packaging.relationship',
        'openpyxl.packaging.workbook',
        'openpyxl.workbook.protection',
        'openpyxl.workbook.properties',
        'openpyxl.worksheet.protection',
        'openpyxl.worksheet.header_footer',
        'openpyxl.worksheet.page',
        'openpyxl.worksheet.pagebreak',
        'openpyxl.worksheet.filters',
        'openpyxl.worksheet.datavalidation',
        'openpyxl.worksheet.table',
        'openpyxl.worksheet.pivot',
        'openpyxl.styles.fonts',
        'openpyxl.styles.fills',
        'openpyxl.styles.borders',
        'openpyxl.styles.alignment',
        'openpyxl.styles.protection',
        'openpyxl.styles.numbers',
        'openpyxl.styles.colors',
        'openpyxl.styles.named_styles',
        'openpyxl.chart',
        'openpyxl.drawing',
        'openpyxl.comments',
        
        # requests的依赖
        'urllib3',
        'certifi',
        'charset_normalizer',
        'idna',
    ],
    
    # 排除的模块
    excludes=[
        # 不需要的模块
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
        'tensorflow',
        'torch',
        'sklearn',
        'pytest',
        'IPython',
        'jupyter',
    ],
    
    # 钩子目录
    hookspath=[],
    
    # 运行时钩子
    hooksconfig={},
    
    # 运行时临时目录
    runtime_tmpdir=None,
    
    # 不警告缺失的模块
    noarchive=False,
    
    # 优化级别
    optimize=0,
)

# 过滤掉None值
a.datas = [item for item in a.datas if item is not None]

# PYZ文件（Python字节码归档）
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 可执行文件
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    
    # 可执行文件名
    name='旺店通数据自动获取工具',
    
    # 调试选项
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    
    # 运行时选项
    runtime_tmpdir=None,
    
    # 控制台选项
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    
    # 图标文件（如果有的话）
    icon=None,
    
    # 版本信息
    version=None,
    
    # 清单文件
    manifest=None,
    
    # 资源文件
    resources=[],
    
    # UPX压缩
    upx_dir=None,
)

# 如果需要创建目录结构，可以使用COLLECT
# 这里我们创建一个单文件可执行程序，所以不需要COLLECT

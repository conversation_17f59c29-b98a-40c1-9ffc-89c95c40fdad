"""
统计分析模块
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from config import file_config

class StatisticsEngine:
    """统计分析引擎"""
    
    def __init__(self):
        self.logistics_rules = file_config.LOGISTICS_RULES
    
    def analyze_logistics_numbers(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        分析物流单号统计
        
        Args:
            df: 包含物流单号的DataFrame
            
        Returns:
            物流统计信息字典
        """
        if '物流单号' not in df.columns:
            return self._empty_logistics_stats()
        
        # 获取物流单号列，去除空值并转换为字符串
        logistics_numbers = df['物流单号'].dropna().astype(str)
        
        # 原始总数（包含重复）
        original_total = len(logistics_numbers)
        
        # 去重处理：获取唯一的物流单号
        unique_logistics_numbers = logistics_numbers.drop_duplicates()
        
        # 计算重复数量
        duplicate_count = original_total - len(unique_logistics_numbers)
        
        # 按物流公司统计
        logistics_stats = {}
        logistics_samples = {}
        
        for prefix, company in self.logistics_rules.items():
            # 统计该公司的单号
            company_numbers = unique_logistics_numbers[unique_logistics_numbers.str.startswith(prefix)]
            count = len(company_numbers)
            
            if count > 0:
                logistics_stats[company] = count
                # 保存样本单号（最多10个）
                logistics_samples[company] = company_numbers.head(10).tolist()
        
        # 唯一单号总计
        unique_total_count = len(unique_logistics_numbers)
        
        # 生成当前日期
        current_date = datetime.now().strftime('%Y年%m月%d日')
        
        # 生成微信消息
        message_parts = [f"{current_date}快递出货单量如下："]
        for company, count in logistics_stats.items():
            message_parts.append(f"{company}：{count}单")
        message_parts.append(f"快递总单量：{unique_total_count}单")
        
        message = "\n".join(message_parts)
        
        return {
            'logistics_stats': logistics_stats,
            'total_count': original_total,
            'unique_count': unique_total_count,
            'duplicate_count': duplicate_count,
            'message': message,
            'date': current_date,
            'samples': logistics_samples
        }
    
    def _empty_logistics_stats(self) -> Dict[str, Any]:
        """返回空的物流统计"""
        current_date = datetime.now().strftime('%Y年%m月%d日')
        return {
            'logistics_stats': {},
            'total_count': 0,
            'unique_count': 0,
            'duplicate_count': 0,
            'message': f"{current_date}暂无快递出货数据",
            'date': current_date,
            'samples': {}
        }
    
    def analyze_daily_statistics(self, df: pd.DataFrame, date_column: str = '发货时间') -> Dict[str, Any]:
        """
        分析每日统计数据
        
        Args:
            df: 数据DataFrame
            date_column: 日期列名
            
        Returns:
            每日统计信息
        """
        try:
            if date_column not in df.columns:
                return {}
            
            # 转换日期列
            df[date_column] = pd.to_datetime(df[date_column])
            df['日期'] = df[date_column].dt.date
            
            # 按日期统计
            daily_stats = df.groupby('日期').agg({
                '货品数量': 'sum',
                '物流单号': 'nunique'  # 去重计数
            }).reset_index()
            
            daily_stats.columns = ['日期', '总数量', '单号数量']
            
            return {
                'daily_data': daily_stats.to_dict('records'),
                'total_days': len(daily_stats),
                'avg_daily_quantity': daily_stats['总数量'].mean(),
                'avg_daily_orders': daily_stats['单号数量'].mean()
            }
            
        except Exception as e:
            return {}
    
    def analyze_owner_statistics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        分析货主统计数据
        
        Args:
            df: 数据DataFrame
            
        Returns:
            货主统计信息
        """
        try:
            if '货主' not in df.columns:
                return {}
            
            # 按货主统计
            owner_stats = df.groupby('货主').agg({
                '货品数量': 'sum',
                '物流单号': 'nunique'
            }).reset_index()
            
            owner_stats.columns = ['货主', '总数量', '单号数量']
            owner_stats = owner_stats.sort_values('总数量', ascending=False)
            
            return {
                'owner_data': owner_stats.to_dict('records'),
                'total_owners': len(owner_stats),
                'top_owner': owner_stats.iloc[0].to_dict() if len(owner_stats) > 0 else None
            }
            
        except Exception as e:
            return {}
    
    def analyze_category_statistics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        分析分类统计数据
        
        Args:
            df: 数据DataFrame
            
        Returns:
            分类统计信息
        """
        try:
            if '分类' not in df.columns:
                return {}
            
            # 按分类统计
            category_stats = df.groupby('分类').agg({
                '货品数量': 'sum',
                '物流单号': 'nunique'
            }).reset_index()
            
            category_stats.columns = ['分类', '总数量', '单号数量']
            category_stats = category_stats.sort_values('总数量', ascending=False)
            
            return {
                'category_data': category_stats.to_dict('records'),
                'total_categories': len(category_stats),
                'top_category': category_stats.iloc[0].to_dict() if len(category_stats) > 0 else None
            }
            
        except Exception as e:
            return {}
    
    def generate_summary_report(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        生成综合统计报告
        
        Args:
            df: 数据DataFrame
            
        Returns:
            综合统计报告
        """
        report = {
            'basic_info': {
                'total_records': len(df),
                'date_range': self._get_date_range(df),
                'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            },
            'logistics': self.analyze_logistics_numbers(df),
            'owners': self.analyze_owner_statistics(df),
            'categories': self.analyze_category_statistics(df)
        }
        
        # 如果有日期列，添加每日统计
        if '发货时间' in df.columns:
            report['daily'] = self.analyze_daily_statistics(df)
        
        return report
    
    def _get_date_range(self, df: pd.DataFrame) -> Dict[str, str]:
        """获取数据的日期范围"""
        try:
            if '发货时间' in df.columns:
                dates = pd.to_datetime(df['发货时间'])
                return {
                    'start_date': dates.min().strftime('%Y-%m-%d'),
                    'end_date': dates.max().strftime('%Y-%m-%d')
                }
        except:
            pass
        
        return {
            'start_date': '',
            'end_date': ''
        }

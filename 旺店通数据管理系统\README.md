# 旺店通数据管理系统

[![Python Version](https://img.shields.io/badge/python-3.8+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Platform](https://img.shields.io/badge/platform-Windows-lightgrey.svg)](https://www.microsoft.com/windows)

## 🚀 项目概述

旺店通数据管理系统是一个集成的企业级数据管理解决方案，专为旺店通WMS用户设计。系统融合了API数据获取、智能数据处理、统计分析和微信自动发送功能，并提供强大的月度出货数据统计功能。

### ✨ 核心特性

- **🔄 自动化数据获取**: 定时从旺店通API获取出库数据，支持多种定时模式
- **📊 智能数据处理**: 实时监控文件变化，自动生成数据透视表和统计分析
- **📈 月度统计报告**: 可自定义月份的深度数据分析，支持多维度统计和可视化图表
- **📱 微信自动发送**: 智能发送报告到多个微信联系人，支持多种格式
- **🖥️ 现代化界面**: 基于tkinter的直观GUI，实时日志和状态监控
- **⚙️ 灵活配置**: 图形化配置管理，支持配置导入导出

## 📋 功能详解

### 🔄 API数据获取
- **自动数据获取**: 定时从旺店通API获取出库数据
- **智能映射**: 自动匹配货品档案信息
- **多种定时模式**: 支持间隔定时和每日定时
- **批量处理**: 自动处理分页数据，支持大数据量获取
- **错误重试**: 智能重试机制，确保数据获取稳定性

### 📊 数据处理与分析
- **实时监控**: 监控Excel文件变化，自动触发数据处理
- **数据透视表**: 自动生成按货主和分类统计的数据透视表
- **物流统计**: 智能识别并统计顺丰、京东等快递公司单量
- **去重处理**: 智能去除重复物流单号，确保数据准确性
- **格式美化**: 自动美化Excel格式，提升报告专业度

### 📈 月度统计功能（新增）
- **自定义月份**: 用户可选择任意年份和月份进行统计
- **多数据源**: 支持单文件或目录批量数据源
- **多维度分析**:
  - 货主维度：按货主统计出货数据
  - 分类维度：按商品分类统计
  - 物流维度：按物流公司统计单量
  - 时间维度：按日期统计趋势
- **可视化图表**:
  - 柱状图：适合比较分析
  - 折线图：适合趋势分析
  - 饼图：适合比例分析
  - 面积图：适合累积分析
- **报告导出**: 自动生成Excel和图片格式报告

### 📱 微信自动发送
- **多人发送**: 支持同时发送给多个微信联系人
- **多种格式**: 支持Excel文件和图片格式发送
- **统计消息**: 自动发送物流统计文本消息
- **发送状态**: 实时显示发送进度和状态
- **智能转换**: Excel自动转换为图片格式（可选）

### 🖥️ 用户界面
- **现代化GUI**: 基于tkinter的现代化界面设计
- **实时日志**: 彩色日志显示，支持不同级别的消息
- **状态监控**: 实时显示各功能模块运行状态
- **配置管理**: 图形化配置管理界面
- **主题支持**: 支持明暗主题切换（需要sv-ttk）

## 🏗️ 技术架构

### 核心模块架构
```
旺店通数据管理系统/
├── core/                   # 核心功能模块
│   ├── api_client.py      # 旺店通API客户端
│   ├── data_processor.py  # 数据处理引擎
│   ├── statistics.py      # 统计分析模块
│   ├── wechat_sender.py   # 微信发送模块
│   └── monthly_report.py  # 月度报告生成器
├── gui/                    # 图形界面模块
│   ├── main_window.py     # 主窗口界面
│   ├── config_dialog.py   # 配置对话框
│   └── monthly_dialog.py  # 月度统计对话框
├── utils/                  # 工具模块
│   ├── config_manager.py  # 配置管理器
│   ├── file_monitor.py    # 文件监控器
│   └── excel_utils.py     # Excel工具函数
├── main.py                # 主程序入口
├── config.py              # 配置文件
└── requirements.txt       # 依赖包列表
```

### 技术栈
- **后端**: Python 3.8+
- **GUI框架**: tkinter + ttk
- **数据处理**: pandas + openpyxl
- **API通信**: requests
- **图表生成**: matplotlib + seaborn
- **微信自动化**: wxauto
- **配置管理**: python-dotenv + json

## 📦 安装和部署

### 环境要求
- **操作系统**: Windows 10/11
- **Python版本**: 3.8 或更高版本
- **微信客户端**: 用于自动发送功能
- **内存**: 建议 4GB 以上
- **磁盘空间**: 建议 1GB 以上

### 快速安装

#### 方式一：自动安装（推荐）
```bash
# 1. 下载项目
git clone <repository-url>
cd 旺店通数据管理系统

# 2. 运行自动安装脚本
python run.py
```

#### 方式二：手动安装
```bash
# 1. 安装依赖包
pip install -r requirements.txt

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填入API配置

# 3. 启动程序
python main.py
```

### 配置说明

#### API配置
1. 复制 `.env.example` 为 `.env`
2. 填入旺店通API参数：
   ```env
   WDT_SID=你的SID
   WDT_APP_KEY=你的APP_KEY
   WDT_APP_SECRET=你的APP_SECRET
   ```

#### 货品档案配置
- 准备Excel格式的货品档案文件
- 必须包含"货品名称"列
- 可选包含"货品编号"和"备注"列

#### 微信配置
- 确保微信客户端已登录
- 联系人名称必须与微信显示完全一致
- 建议先测试连接再正式使用

## 🎯 使用指南

### 快速开始

1. **启动程序**
   ```bash
   python run.py  # 推荐，包含启动检查
   # 或
   python main.py  # 直接启动
   ```

2. **基础配置**
   - 设置API参数和输出路径
   - 配置货品档案文件
   - 设置微信联系人

3. **开始使用**
   - 启动API定时任务获取数据
   - 开启文件监控自动处理
   - 生成月度统计报告

### 详细使用流程

#### 日常数据处理
1. 在"API设置"区域配置输出路径和货品档案
2. 点击"测试连接"验证API配置
3. 点击"启动定时任务"开始自动获取数据
4. 在"监控设置"区域配置微信联系人
5. 点击"开始监控"启动文件监控
6. 系统将自动处理数据并发送报告

#### 月度报告生成
1. 点击"生成月度报告"按钮
2. 选择统计年份和月份
3. 选择数据源（文件或目录）
4. 选择统计维度和图表类型
5. 设置输出目录
6. 点击"生成报告"开始生成
7. 查看生成的Excel和图表文件

### 高级功能

#### 配置管理
- 点击"配置设置"进入高级配置
- 支持API、路径、监控、微信、界面等全方位配置
- 支持配置导入导出和重置功能

#### 批量处理
- 支持目录批量数据源
- 自动合并多个Excel文件
- 智能处理大数据量

## 🧪 测试和验证

### 运行测试套件
```bash
# 运行完整测试套件
python test_system.py

# 测试月度报告功能
python test_monthly_report.py

# 创建示例数据
python create_sample_data.py
```

### 功能验证清单
- [ ] API连接测试通过
- [ ] 数据获取和处理正常
- [ ] 文件监控功能正常
- [ ] 微信发送功能正常
- [ ] 月度报告生成正常
- [ ] 配置保存和加载正常

## 📦 打包和部署

### 创建可执行文件
```bash
# 运行打包工具
python build.py

# 选择打包选项：
# 1. 构建可执行文件
# 2. 创建便携版包
# 3. 清理构建文件
```

### 部署选项
1. **可执行文件版本**: 适合最终用户，无需Python环境
2. **便携版本**: 适合开发者，包含完整源代码
3. **源码版本**: 适合定制开发

## 🔧 故障排除

### 常见问题

**API连接失败**
- 检查网络连接和防火墙设置
- 验证API参数配置是否正确
- 确认API权限和配额限制

**文件处理失败**
- 确保Excel文件未被其他程序占用
- 检查文件格式和必需列是否存在
- 验证文件路径和权限设置

**微信发送失败**
- 确认微信客户端已登录并在前台
- 检查联系人名称是否完全匹配
- 尝试重启微信客户端

**图表生成失败**
- 安装图表依赖：`pip install matplotlib seaborn`
- 检查数据是否包含必要的列
- 确认输出目录有写入权限

### 日志分析
- 实时日志：程序界面底部显示
- 详细日志：`logs/wdt_system.log` 文件
- 错误日志：包含完整堆栈信息

### 性能优化
- 调整API批量大小和请求间隔
- 定期清理临时文件和日志
- 关闭不必要的功能模块

## 📄 更新日志

### v1.0.0 (2025-08-04)
- ✨ 融合API数据获取和数据处理功能
- 🆕 新增月度出货数据统计功能
- 🎨 优化用户界面和用户体验
- 🐛 完善错误处理和日志记录
- 📚 添加完整的文档和测试套件
- 🔧 支持配置管理和打包部署

## 🤝 贡献指南

欢迎提交问题报告和功能建议！

### 开发环境设置
1. Fork 项目仓库
2. 创建功能分支
3. 安装开发依赖
4. 运行测试套件
5. 提交 Pull Request

### 代码规范
- 遵循 PEP 8 代码风格
- 添加适当的注释和文档
- 编写单元测试
- 更新相关文档

## 📞 技术支持

### 获取帮助
- 📖 查看详细使用说明：`使用说明.md`
- 🐛 提交问题：GitHub Issues
- 💬 技术讨论：联系开发团队

### 联系方式
- 开发团队：Augment Agent
- 技术支持：请通过GitHub Issues联系

## 📜 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

**⭐ 如果这个项目对您有帮助，请给我们一个星标！**

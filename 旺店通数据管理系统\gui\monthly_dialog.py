"""
月度统计对话框
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import os
from datetime import datetime, date
from typing import List, Dict, Any
from config import app_config

class MonthlyDialog:
    """月度统计对话框"""
    
    def __init__(self, parent, app_instance):
        self.parent = parent
        self.app = app_instance
        self.dialog = None
        
        # 变量
        self.year_var = tk.IntVar(value=datetime.now().year)
        self.month_var = tk.IntVar(value=datetime.now().month)
        self.data_source_var = tk.StringVar()
        self.output_dir_var = tk.StringVar()
        
        # 统计维度变量
        self.dimension_vars = {}
        for key, name in app_config.STATISTICS_DIMENSIONS.items():
            self.dimension_vars[key] = tk.BooleanVar(value=True)
        
        # 图表类型变量
        self.chart_vars = {}
        for key, name in app_config.CHART_TYPES.items():
            self.chart_vars[key] = tk.BooleanVar(value=key in ['bar', 'pie'])
        
        self.progress_var = tk.StringVar(value="就绪")
        self.generating = False
    
    def show(self):
        """显示对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("月度出货数据统计")
        self.dialog.geometry("600x700")
        self.dialog.resizable(False, False)
        
        # 设置为模态对话框
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (
            self.parent.winfo_rootx() + 50,
            self.parent.winfo_rooty() + 50
        ))
        
        self._create_widgets()
        self._load_default_values()
    
    def _create_widgets(self):
        """创建界面组件"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建各个区域
        self._create_date_selection(main_frame)
        self._create_data_source_selection(main_frame)
        self._create_output_selection(main_frame)
        self._create_dimensions_selection(main_frame)
        self._create_chart_selection(main_frame)
        self._create_progress_area(main_frame)
        self._create_button_area(main_frame)
    
    def _create_date_selection(self, parent):
        """创建日期选择区域"""
        date_frame = ttk.LabelFrame(parent, text="选择统计月份", padding="10")
        date_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 年份选择
        ttk.Label(date_frame, text="年份:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        year_combo = ttk.Combobox(date_frame, textvariable=self.year_var, width=10)
        year_combo['values'] = list(range(2020, datetime.now().year + 2))
        year_combo.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))
        
        # 月份选择
        ttk.Label(date_frame, text="月份:").grid(row=0, column=2, sticky=tk.W, padx=(0, 10))
        month_combo = ttk.Combobox(date_frame, textvariable=self.month_var, width=10)
        month_combo['values'] = list(range(1, 13))
        month_combo.grid(row=0, column=3, sticky=tk.W)
        
        # 快速选择按钮
        quick_frame = ttk.Frame(date_frame)
        quick_frame.grid(row=1, column=0, columnspan=4, pady=(10, 0))
        
        ttk.Button(quick_frame, text="本月", command=self._select_current_month).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(quick_frame, text="上月", command=self._select_last_month).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(quick_frame, text="去年同期", command=self._select_last_year_same_month).pack(side=tk.LEFT)
    
    def _create_data_source_selection(self, parent):
        """创建数据源选择区域"""
        source_frame = ttk.LabelFrame(parent, text="数据源设置", padding="10")
        source_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 数据源路径
        ttk.Label(source_frame, text="数据源:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        ttk.Label(source_frame, text="(可以是单个Excel文件或包含多个Excel文件的目录)", 
                 font=('TkDefaultFont', 8)).grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(0, 5))
        
        source_entry = ttk.Entry(source_frame, textvariable=self.data_source_var, width=50)
        source_entry.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        button_frame = ttk.Frame(source_frame)
        button_frame.grid(row=2, column=1, padx=(10, 0))
        
        ttk.Button(button_frame, text="选择文件", command=self._browse_data_file).pack(side=tk.TOP, pady=(0, 2))
        ttk.Button(button_frame, text="选择目录", command=self._browse_data_dir).pack(side=tk.TOP)
        
        source_frame.columnconfigure(0, weight=1)
    
    def _create_output_selection(self, parent):
        """创建输出选择区域"""
        output_frame = ttk.LabelFrame(parent, text="输出设置", padding="10")
        output_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 输出目录
        ttk.Label(output_frame, text="输出目录:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        output_entry = ttk.Entry(output_frame, textvariable=self.output_dir_var, width=50)
        output_entry.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        ttk.Button(output_frame, text="浏览", command=self._browse_output_dir).grid(row=1, column=1, padx=(10, 0))
        
        output_frame.columnconfigure(0, weight=1)
    
    def _create_dimensions_selection(self, parent):
        """创建统计维度选择区域"""
        dim_frame = ttk.LabelFrame(parent, text="统计维度", padding="10")
        dim_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(dim_frame, text="选择要统计的维度:").pack(anchor=tk.W, pady=(0, 5))
        
        # 创建复选框
        checkbox_frame = ttk.Frame(dim_frame)
        checkbox_frame.pack(fill=tk.X)
        
        col = 0
        for key, name in app_config.STATISTICS_DIMENSIONS.items():
            ttk.Checkbutton(checkbox_frame, text=name, variable=self.dimension_vars[key]).grid(
                row=0, column=col, sticky=tk.W, padx=(0, 20)
            )
            col += 1
        
        # 全选/全不选按钮
        button_frame = ttk.Frame(dim_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="全选", command=self._select_all_dimensions).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="全不选", command=self._deselect_all_dimensions).pack(side=tk.LEFT)
    
    def _create_chart_selection(self, parent):
        """创建图表类型选择区域"""
        chart_frame = ttk.LabelFrame(parent, text="图表类型", padding="10")
        chart_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 检查图表功能可用性
        if not self.app.monthly_report_generator.is_chart_available():
            ttk.Label(chart_frame, text="图表功能不可用（缺少matplotlib或seaborn）", 
                     foreground="red").pack(anchor=tk.W)
            return
        
        ttk.Label(chart_frame, text="选择要生成的图表类型:").pack(anchor=tk.W, pady=(0, 5))
        
        # 创建复选框
        checkbox_frame = ttk.Frame(chart_frame)
        checkbox_frame.pack(fill=tk.X)
        
        col = 0
        for key, name in app_config.CHART_TYPES.items():
            ttk.Checkbutton(checkbox_frame, text=name, variable=self.chart_vars[key]).grid(
                row=0, column=col, sticky=tk.W, padx=(0, 20)
            )
            col += 1
        
        # 全选/全不选按钮
        button_frame = ttk.Frame(chart_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="全选", command=self._select_all_charts).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="全不选", command=self._deselect_all_charts).pack(side=tk.LEFT)
    
    def _create_progress_area(self, parent):
        """创建进度区域"""
        progress_frame = ttk.LabelFrame(parent, text="生成进度", padding="10")
        progress_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 进度条
        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, pady=(0, 5))
        
        # 状态标签
        ttk.Label(progress_frame, textvariable=self.progress_var).pack(anchor=tk.W)
    
    def _create_button_area(self, parent):
        """创建按钮区域"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 生成按钮
        self.generate_btn = ttk.Button(button_frame, text="生成报告", command=self._generate_report)
        self.generate_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 预览按钮
        ttk.Button(button_frame, text="预览数据", command=self._preview_data).pack(side=tk.LEFT, padx=(0, 10))
        
        # 关闭按钮
        ttk.Button(button_frame, text="关闭", command=self._close_dialog).pack(side=tk.RIGHT)
    
    def _load_default_values(self):
        """加载默认值"""
        # 设置默认数据源为输出路径的目录
        output_path = self.app.config_manager.get('paths.output_path', '')
        if output_path:
            self.data_source_var.set(os.path.dirname(output_path))
        
        # 设置默认输出目录
        default_output = self.app.config_manager.get('paths.monthly_report_path', '月度报告')
        self.output_dir_var.set(default_output)
    
    # 事件处理方法
    def _select_current_month(self):
        """选择当前月份"""
        now = datetime.now()
        self.year_var.set(now.year)
        self.month_var.set(now.month)
    
    def _select_last_month(self):
        """选择上个月"""
        now = datetime.now()
        if now.month == 1:
            self.year_var.set(now.year - 1)
            self.month_var.set(12)
        else:
            self.year_var.set(now.year)
            self.month_var.set(now.month - 1)
    
    def _select_last_year_same_month(self):
        """选择去年同期"""
        now = datetime.now()
        self.year_var.set(now.year - 1)
        self.month_var.set(now.month)
    
    def _browse_data_file(self):
        """浏览数据文件"""
        filename = filedialog.askopenfilename(
            title="选择数据源Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
        )
        if filename:
            self.data_source_var.set(filename)
    
    def _browse_data_dir(self):
        """浏览数据目录"""
        dirname = filedialog.askdirectory(title="选择包含Excel文件的目录")
        if dirname:
            self.data_source_var.set(dirname)
    
    def _browse_output_dir(self):
        """浏览输出目录"""
        dirname = filedialog.askdirectory(title="选择输出目录")
        if dirname:
            self.output_dir_var.set(dirname)
    
    def _select_all_dimensions(self):
        """全选统计维度"""
        for var in self.dimension_vars.values():
            var.set(True)
    
    def _deselect_all_dimensions(self):
        """全不选统计维度"""
        for var in self.dimension_vars.values():
            var.set(False)
    
    def _select_all_charts(self):
        """全选图表类型"""
        for var in self.chart_vars.values():
            var.set(True)
    
    def _deselect_all_charts(self):
        """全不选图表类型"""
        for var in self.chart_vars.values():
            var.set(False)
    
    def _preview_data(self):
        """预览数据"""
        data_source = self.data_source_var.get().strip()
        if not data_source:
            messagebox.showerror("错误", "请先选择数据源")
            return
        
        if not os.path.exists(data_source):
            messagebox.showerror("错误", "数据源路径不存在")
            return
        
        try:
            # 加载数据进行预览
            year = self.year_var.get()
            month = self.month_var.get()
            
            monthly_data = self.app.monthly_report_generator._load_monthly_data(data_source, year, month)
            
            if monthly_data.empty:
                messagebox.showinfo("预览结果", f"{year}年{month}月没有找到数据")
            else:
                info = f"""数据预览结果：

时间范围：{year}年{month}月
总记录数：{len(monthly_data)}
数据列：{', '.join(monthly_data.columns.tolist())}

前5行数据：
{monthly_data.head().to_string()}"""
                
                # 创建预览窗口
                preview_window = tk.Toplevel(self.dialog)
                preview_window.title("数据预览")
                preview_window.geometry("800x600")
                
                text_widget = tk.Text(preview_window, wrap=tk.WORD)
                scrollbar = ttk.Scrollbar(preview_window, orient=tk.VERTICAL, command=text_widget.yview)
                text_widget.configure(yscrollcommand=scrollbar.set)
                
                text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
                scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
                
                text_widget.insert(tk.END, info)
                text_widget.config(state=tk.DISABLED)
                
        except Exception as e:
            messagebox.showerror("错误", f"数据预览失败: {str(e)}")
    
    def _generate_report(self):
        """生成报告"""
        if self.generating:
            return
        
        # 验证输入
        if not self._validate_inputs():
            return
        
        self.generating = True
        self.generate_btn.config(state=tk.DISABLED)
        self.progress_bar.start()
        self.progress_var.set("正在生成报告...")
        
        # 在新线程中生成报告
        threading.Thread(target=self._generate_report_thread, daemon=True).start()
    
    def _validate_inputs(self) -> bool:
        """验证输入"""
        data_source = self.data_source_var.get().strip()
        if not data_source:
            messagebox.showerror("错误", "请选择数据源")
            return False
        
        if not os.path.exists(data_source):
            messagebox.showerror("错误", "数据源路径不存在")
            return False
        
        output_dir = self.output_dir_var.get().strip()
        if not output_dir:
            messagebox.showerror("错误", "请选择输出目录")
            return False
        
        # 检查是否选择了统计维度
        selected_dimensions = [key for key, var in self.dimension_vars.items() if var.get()]
        if not selected_dimensions:
            messagebox.showerror("错误", "请至少选择一个统计维度")
            return False
        
        return True
    
    def _generate_report_thread(self):
        """生成报告线程"""
        try:
            # 获取参数
            year = self.year_var.get()
            month = self.month_var.get()
            data_source = self.data_source_var.get().strip()
            output_dir = self.output_dir_var.get().strip()
            
            selected_dimensions = [key for key, var in self.dimension_vars.items() if var.get()]
            selected_charts = [key for key, var in self.chart_vars.items() if var.get()]
            
            # 生成报告
            result = self.app.monthly_report_generator.generate_monthly_report(
                data_source=data_source,
                year=year,
                month=month,
                output_dir=output_dir,
                dimensions=selected_dimensions,
                chart_types=selected_charts
            )
            
            # 在主线程中更新UI
            self.dialog.after(0, self._on_report_generated, result)
            
        except Exception as e:
            self.dialog.after(0, self._on_report_failed, str(e))
    
    def _on_report_generated(self, result: Dict[str, Any]):
        """报告生成完成"""
        self.generating = False
        self.generate_btn.config(state=tk.NORMAL)
        self.progress_bar.stop()
        
        if result['success']:
            self.progress_var.set("报告生成完成")
            
            # 显示结果
            files_info = '\n'.join(result['files'])
            summary = result.get('data_summary', {})
            
            message = f"""报告生成成功！

{result['message']}

生成的文件：
{files_info}

数据摘要：
• 总记录数：{summary.get('total_records', 0)}
• 数据范围：{summary.get('date_range', '未知')}
"""
            
            messagebox.showinfo("成功", message)
            
            # 询问是否打开输出目录
            if messagebox.askyesno("提示", "是否打开输出目录查看报告？"):
                output_dir = self.output_dir_var.get()
                if os.path.exists(output_dir):
                    os.startfile(output_dir)
        else:
            self.progress_var.set("报告生成失败")
            messagebox.showerror("失败", result['message'])
    
    def _on_report_failed(self, error: str):
        """报告生成失败"""
        self.generating = False
        self.generate_btn.config(state=tk.NORMAL)
        self.progress_bar.stop()
        self.progress_var.set("报告生成失败")
        messagebox.showerror("错误", f"报告生成失败: {error}")
    
    def _close_dialog(self):
        """关闭对话框"""
        if self.generating:
            if not messagebox.askyesno("确认", "报告正在生成中，确定要关闭吗？"):
                return
        
        self.dialog.destroy()

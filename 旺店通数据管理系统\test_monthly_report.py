#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试月度报告生成功能
"""

import os
import sys
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.monthly_report import MonthlyReportGenerator
from create_sample_data import create_sample_data, create_goods_archive

def test_monthly_report():
    """测试月度报告生成"""
    
    print("=== 测试月度报告生成功能 ===\n")
    
    # 检查示例数据是否存在
    sample_dir = "sample_data"
    if not os.path.exists(sample_dir):
        print("示例数据不存在，正在生成...")
        create_sample_data()
        create_goods_archive()
        print()
    
    # 初始化月度报告生成器
    generator = MonthlyReportGenerator()
    
    # 检查图表功能可用性
    print(f"图表功能可用: {'是' if generator.is_chart_available() else '否'}")
    print()
    
    # 测试参数
    test_cases = [
        {
            'name': '2024年6月报告（目录数据源）',
            'data_source': sample_dir,
            'year': 2024,
            'month': 6,
            'output_dir': 'test_reports/2024_06',
            'dimensions': ['owner', 'category', 'logistics'],
            'chart_types': ['bar', 'pie'] if generator.is_chart_available() else []
        },
        {
            'name': '2024年12月报告（单文件数据源）',
            'data_source': f'{sample_dir}/2024年12月出货数据.xlsx',
            'year': 2024,
            'month': 12,
            'output_dir': 'test_reports/2024_12',
            'dimensions': ['owner', 'category', 'daily'],
            'chart_types': ['bar', 'line'] if generator.is_chart_available() else []
        }
    ]
    
    # 执行测试
    for i, test_case in enumerate(test_cases, 1):
        print(f"测试 {i}: {test_case['name']}")
        print("-" * 50)
        
        try:
            # 检查数据源
            if not os.path.exists(test_case['data_source']):
                print(f"❌ 数据源不存在: {test_case['data_source']}")
                continue
            
            # 生成报告
            result = generator.generate_monthly_report(
                data_source=test_case['data_source'],
                year=test_case['year'],
                month=test_case['month'],
                output_dir=test_case['output_dir'],
                dimensions=test_case['dimensions'],
                chart_types=test_case['chart_types']
            )
            
            # 显示结果
            if result['success']:
                print(f"✅ {result['message']}")
                print(f"生成的文件:")
                for file_path in result['files']:
                    file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
                    print(f"  - {file_path} ({file_size:,} 字节)")
                
                # 显示数据摘要
                summary = result.get('data_summary', {})
                if summary:
                    print(f"数据摘要:")
                    print(f"  - 总记录数: {summary.get('total_records', 0):,}")
                    print(f"  - 时间范围: {summary.get('date_range', '未知')}")
                    
                    analysis = summary.get('analysis', {})
                    if 'basic' in analysis:
                        basic = analysis['basic']
                        print(f"  - 总数量: {basic.get('total_quantity', 0):,}")
                        print(f"  - 唯一物流单号: {basic.get('unique_logistics', 0):,}")
                    
                    if 'logistics' in analysis:
                        logistics = analysis['logistics'].get('logistics_stats', {})
                        if logistics:
                            print(f"  - 物流统计: {logistics}")
            else:
                print(f"❌ {result['message']}")
            
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
        
        print()
    
    print("=== 测试完成 ===")
    
    # 显示输出目录
    if os.path.exists('test_reports'):
        print(f"\n报告已生成到: {os.path.abspath('test_reports')}")
        print("可以打开该目录查看生成的报告文件")

def test_data_loading():
    """测试数据加载功能"""
    
    print("=== 测试数据加载功能 ===\n")
    
    generator = MonthlyReportGenerator()
    
    # 测试目录数据源
    sample_dir = "sample_data"
    if os.path.exists(sample_dir):
        print("测试目录数据源...")
        monthly_data = generator._load_monthly_data(sample_dir, 2024, 6)
        print(f"2024年6月数据: {len(monthly_data)} 条记录")
        
        if not monthly_data.empty:
            print(f"列名: {list(monthly_data.columns)}")
            print(f"时间范围: {monthly_data['发货时间'].min()} 到 {monthly_data['发货时间'].max()}")
        print()
    
    # 测试单文件数据源
    sample_file = f"{sample_dir}/2024年06月出货数据.xlsx"
    if os.path.exists(sample_file):
        print("测试单文件数据源...")
        monthly_data = generator._load_monthly_data(sample_file, 2024, 6)
        print(f"单文件数据: {len(monthly_data)} 条记录")
        print()

def test_analysis():
    """测试统计分析功能"""
    
    print("=== 测试统计分析功能 ===\n")
    
    generator = MonthlyReportGenerator()
    
    # 加载测试数据
    sample_dir = "sample_data"
    if not os.path.exists(sample_dir):
        print("示例数据不存在，请先运行 create_sample_data.py")
        return
    
    monthly_data = generator._load_monthly_data(sample_dir, 2024, 6)
    if monthly_data.empty:
        print("没有找到测试数据")
        return
    
    print(f"分析数据: {len(monthly_data)} 条记录")
    
    # 测试统计分析
    analysis = generator._generate_analysis(monthly_data, ['owner', 'category', 'logistics', 'daily'])
    
    print("分析结果:")
    
    # 基础统计
    if 'basic' in analysis:
        basic = analysis['basic']
        print(f"  基础统计:")
        print(f"    - 总记录数: {basic['total_records']:,}")
        print(f"    - 总数量: {basic['total_quantity']:,}")
        print(f"    - 唯一物流单号: {basic['unique_logistics']:,}")
    
    # 物流统计
    if 'logistics' in analysis:
        logistics_stats = analysis['logistics'].get('logistics_stats', {})
        if logistics_stats:
            print(f"  物流统计:")
            for company, count in logistics_stats.items():
                print(f"    - {company}: {count:,} 单")
    
    # 货主统计
    if 'owners' in analysis:
        owner_data = analysis['owners'].get('owner_data', [])
        if owner_data:
            print(f"  货主统计 (前5名):")
            for owner in owner_data[:5]:
                print(f"    - {owner['货主']}: {owner['总数量']:,} 件, {owner['单号数量']:,} 单")
    
    # 分类统计
    if 'categories' in analysis:
        category_data = analysis['categories'].get('category_data', [])
        if category_data:
            print(f"  分类统计:")
            for category in category_data:
                print(f"    - {category['分类']}: {category['总数量']:,} 件")

if __name__ == "__main__":
    print("旺店通月度报告功能测试\n")
    
    # 检查依赖
    try:
        import matplotlib.pyplot as plt
        import seaborn as sns
        print("✅ 图表依赖包可用")
    except ImportError:
        print("⚠️ 图表依赖包不可用，将跳过图表生成")
    
    print()
    
    # 运行测试
    test_data_loading()
    test_analysis()
    test_monthly_report()
    
    print("\n测试完成！")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
旺店通数据管理系统打包脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否安装"""
    try:
        import PyInstaller
        return True
    except ImportError:
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
        return True
    except subprocess.CalledProcessError:
        return False

def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.py', '.'),
        ('requirements.txt', '.'),
        ('.env.example', '.'),
        ('使用说明.md', '.'),
        ('README.md', '.'),
        ('gui', 'gui'),
        ('core', 'core'),
        ('utils', 'utils'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'pandas',
        'openpyxl',
        'requests',
        'python-dotenv',
        'wxauto',
        'matplotlib',
        'seaborn',
        'sv-ttk',
        'pillow',
        'numpy',
        'win32com.client',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='旺店通数据管理系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='旺店通数据管理系统',
)
'''
    
    with open('wdt_system.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    return 'wdt_system.spec'

def build_executable():
    """构建可执行文件"""
    try:
        # 创建规格文件
        spec_file = create_spec_file()
        
        # 运行PyInstaller
        cmd = [
            'pyinstaller',
            '--clean',
            '--noconfirm',
            spec_file
        ]
        
        subprocess.check_call(cmd)
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"构建失败: {e}")
        return False

def create_installer():
    """创建安装包"""
    dist_dir = Path('dist/旺店通数据管理系统')
    if not dist_dir.exists():
        print("构建目录不存在，请先运行构建")
        return False
    
    # 复制额外文件
    extra_files = [
        '使用说明.md',
        'README.md',
        '.env.example',
        'requirements.txt'
    ]
    
    for file_name in extra_files:
        if os.path.exists(file_name):
            shutil.copy2(file_name, dist_dir)
    
    # 创建启动脚本
    startup_script = dist_dir / '启动.bat'
    with open(startup_script, 'w', encoding='gbk') as f:
        f.write('@echo off\n')
        f.write('cd /d "%~dp0"\n')
        f.write('start "" "旺店通数据管理系统.exe"\n')
    
    # 创建示例数据脚本
    sample_script = dist_dir / '创建示例数据.bat'
    with open(sample_script, 'w', encoding='gbk') as f:
        f.write('@echo off\n')
        f.write('cd /d "%~dp0"\n')
        f.write('echo 正在创建示例数据...\n')
        f.write('python create_sample_data.py\n')
        f.write('pause\n')
    
    print(f"安装包已创建: {dist_dir}")
    return True

def create_portable_package():
    """创建便携版包"""
    try:
        # 创建便携版目录
        portable_dir = Path('portable_package')
        if portable_dir.exists():
            shutil.rmtree(portable_dir)
        
        portable_dir.mkdir()
        
        # 复制源代码
        source_files = [
            'main.py',
            'run.py',
            'config.py',
            'requirements.txt',
            '.env.example',
            '使用说明.md',
            'README.md',
            'create_sample_data.py',
            'export_to_wdt_data.py',
            'test_monthly_report.py',
            'test_system.py'
        ]
        
        for file_name in source_files:
            if os.path.exists(file_name):
                shutil.copy2(file_name, portable_dir)
        
        # 复制目录
        source_dirs = ['core', 'gui', 'utils']
        for dir_name in source_dirs:
            if os.path.exists(dir_name):
                shutil.copytree(dir_name, portable_dir / dir_name)
        
        # 创建安装脚本
        install_script = portable_dir / '安装依赖.bat'
        with open(install_script, 'w', encoding='gbk') as f:
            f.write('@echo off\n')
            f.write('echo 正在安装Python依赖包...\n')
            f.write('pip install -r requirements.txt\n')
            f.write('echo 安装完成！\n')
            f.write('pause\n')
        
        # 创建启动脚本
        run_script = portable_dir / '启动程序.bat'
        with open(run_script, 'w', encoding='gbk') as f:
            f.write('@echo off\n')
            f.write('python run.py\n')
        
        print(f"便携版包已创建: {portable_dir}")
        return True
        
    except Exception as e:
        print(f"创建便携版包失败: {e}")
        return False

def clean_build():
    """清理构建文件"""
    clean_dirs = ['build', 'dist', '__pycache__']
    clean_files = ['*.spec']
    
    for dir_name in clean_dirs:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"已删除: {dir_name}")
    
    import glob
    for pattern in clean_files:
        for file_path in glob.glob(pattern):
            os.remove(file_path)
            print(f"已删除: {file_path}")

def main():
    """主函数"""
    print("旺店通数据管理系统打包工具")
    print("=" * 40)
    
    while True:
        print("\n请选择操作:")
        print("1. 构建可执行文件")
        print("2. 创建便携版包")
        print("3. 清理构建文件")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            print("\n开始构建可执行文件...")
            
            # 检查PyInstaller
            if not check_pyinstaller():
                print("PyInstaller未安装，正在安装...")
                if not install_pyinstaller():
                    print("PyInstaller安装失败")
                    continue
            
            # 构建
            if build_executable():
                print("✅ 可执行文件构建成功")
                
                # 询问是否创建安装包
                create_pkg = input("是否创建安装包? (y/n): ").strip().lower()
                if create_pkg == 'y':
                    if create_installer():
                        print("✅ 安装包创建成功")
                    else:
                        print("❌ 安装包创建失败")
            else:
                print("❌ 可执行文件构建失败")
        
        elif choice == '2':
            print("\n开始创建便携版包...")
            if create_portable_package():
                print("✅ 便携版包创建成功")
            else:
                print("❌ 便携版包创建失败")
        
        elif choice == '3':
            print("\n开始清理构建文件...")
            clean_build()
            print("✅ 清理完成")
        
        elif choice == '4':
            print("退出打包工具")
            break
        
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()

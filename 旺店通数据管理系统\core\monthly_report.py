"""
月度报告生成器
"""
import pandas as pd
import numpy as np
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from config import app_config

# 检查图表功能可用性
try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    CHART_AVAILABLE = True
except ImportError:
    CHART_AVAILABLE = False

class MonthlyReportGenerator:
    """月度报告生成器"""
    
    def __init__(self):
        self.chart_available = CHART_AVAILABLE
        if CHART_AVAILABLE:
            # 设置中文字体和样式
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
            plt.rcParams['axes.unicode_minus'] = False
            sns.set_style("whitegrid")
    
    def is_chart_available(self) -> bool:
        """检查图表功能是否可用"""
        return self.chart_available
    
    def generate_monthly_report(
        self,
        data_source: str,
        year: int,
        month: int,
        output_dir: str,
        dimensions: List[str] = None,
        chart_types: List[str] = None
    ) -> Dict[str, Any]:
        """
        生成月度报告
        
        Args:
            data_source: 数据源文件路径或目录
            year: 年份
            month: 月份
            output_dir: 输出目录
            dimensions: 统计维度列表
            chart_types: 图表类型列表
            
        Returns:
            报告生成结果
        """
        try:
            # 加载月度数据
            monthly_data = self._load_monthly_data(data_source, year, month)
            
            if monthly_data.empty:
                return {
                    'success': False,
                    'message': f'{year}年{month}月没有找到数据',
                    'files': []
                }
            
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成报告文件名前缀
            report_prefix = f"{year}年{month:02d}月出货数据报告"
            
            # 生成统计分析
            analysis_result = self._generate_analysis(monthly_data, dimensions or ['owner', 'category', 'logistics'])
            
            # 生成Excel报告
            excel_file = self._generate_excel_report(monthly_data, analysis_result, output_dir, report_prefix)
            
            generated_files = [excel_file]
            
            # 生成图表（如果可用）
            if self.chart_available and chart_types:
                chart_files = self._generate_charts(monthly_data, analysis_result, output_dir, report_prefix, chart_types)
                generated_files.extend(chart_files)
            
            return {
                'success': True,
                'message': f'成功生成{year}年{month}月报告',
                'files': generated_files,
                'data_summary': {
                    'total_records': len(monthly_data),
                    'date_range': f"{year}-{month:02d}-01 到 {year}-{month:02d}-{self._get_month_last_day(year, month)}",
                    'analysis': analysis_result
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'生成月度报告失败: {str(e)}',
                'files': []
            }
    
    def _load_monthly_data(self, data_source: str, year: int, month: int) -> pd.DataFrame:
        """加载指定月份的数据"""
        all_data = []
        
        if os.path.isfile(data_source):
            # 单个文件
            if data_source.endswith(('.xlsx', '.xls')):
                df = pd.read_excel(data_source)
                all_data.append(df)
        elif os.path.isdir(data_source):
            # 目录中的所有Excel文件
            for file_name in os.listdir(data_source):
                if file_name.endswith(('.xlsx', '.xls')):
                    file_path = os.path.join(data_source, file_name)
                    try:
                        df = pd.read_excel(file_path)
                        all_data.append(df)
                    except Exception as e:
                        continue
        
        if not all_data:
            return pd.DataFrame()
        
        # 合并所有数据
        combined_data = pd.concat(all_data, ignore_index=True)
        
        # 过滤指定月份的数据
        if '发货时间' in combined_data.columns:
            try:
                combined_data['发货时间'] = pd.to_datetime(combined_data['发货时间'])
                monthly_data = combined_data[
                    (combined_data['发货时间'].dt.year == year) &
                    (combined_data['发货时间'].dt.month == month)
                ]
                return monthly_data
            except:
                pass
        
        return combined_data
    
    def _generate_analysis(self, df: pd.DataFrame, dimensions: List[str]) -> Dict[str, Any]:
        """生成统计分析"""
        from .statistics import StatisticsEngine
        
        stats_engine = StatisticsEngine()
        analysis = {}
        
        # 基础统计
        analysis['basic'] = {
            'total_records': len(df),
            'total_quantity': df['货品数量'].sum() if '货品数量' in df.columns else 0,
            'unique_logistics': df['物流单号'].nunique() if '物流单号' in df.columns else 0
        }
        
        # 物流统计
        if 'logistics' in dimensions:
            analysis['logistics'] = stats_engine.analyze_logistics_numbers(df)
        
        # 货主统计
        if 'owner' in dimensions and '货主' in df.columns:
            analysis['owners'] = stats_engine.analyze_owner_statistics(df)
        
        # 分类统计
        if 'category' in dimensions and '分类' in df.columns:
            analysis['categories'] = stats_engine.analyze_category_statistics(df)
        
        # 每日统计
        if 'daily' in dimensions and '发货时间' in df.columns:
            analysis['daily'] = stats_engine.analyze_daily_statistics(df)
        
        return analysis
    
    def _generate_excel_report(self, df: pd.DataFrame, analysis: Dict, output_dir: str, prefix: str) -> str:
        """生成Excel报告"""
        excel_file = os.path.join(output_dir, f"{prefix}.xlsx")
        
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            # 原始数据
            df.to_excel(writer, sheet_name='原始数据', index=False)
            
            # 汇总统计
            summary_data = []
            if 'basic' in analysis:
                basic = analysis['basic']
                summary_data.extend([
                    ['总记录数', basic['total_records']],
                    ['总数量', basic['total_quantity']],
                    ['唯一物流单号数', basic['unique_logistics']]
                ])
            
            if summary_data:
                summary_df = pd.DataFrame(summary_data, columns=['指标', '数值'])
                summary_df.to_excel(writer, sheet_name='汇总统计', index=False)
            
            # 货主统计
            if 'owners' in analysis and analysis['owners'].get('owner_data'):
                owner_df = pd.DataFrame(analysis['owners']['owner_data'])
                owner_df.to_excel(writer, sheet_name='货主统计', index=False)
            
            # 分类统计
            if 'categories' in analysis and analysis['categories'].get('category_data'):
                category_df = pd.DataFrame(analysis['categories']['category_data'])
                category_df.to_excel(writer, sheet_name='分类统计', index=False)
            
            # 物流统计
            if 'logistics' in analysis and analysis['logistics'].get('logistics_stats'):
                logistics_data = []
                for company, count in analysis['logistics']['logistics_stats'].items():
                    logistics_data.append([company, count])
                if logistics_data:
                    logistics_df = pd.DataFrame(logistics_data, columns=['物流公司', '单量'])
                    logistics_df.to_excel(writer, sheet_name='物流统计', index=False)
            
            # 每日统计
            if 'daily' in analysis and analysis['daily'].get('daily_data'):
                daily_df = pd.DataFrame(analysis['daily']['daily_data'])
                daily_df.to_excel(writer, sheet_name='每日统计', index=False)
        
        return excel_file
    
    def _generate_charts(self, df: pd.DataFrame, analysis: Dict, output_dir: str, prefix: str, chart_types: List[str]) -> List[str]:
        """生成图表"""
        if not self.chart_available:
            return []
        
        chart_files = []
        
        # 货主统计图表
        if 'owners' in analysis and analysis['owners'].get('owner_data'):
            owner_data = analysis['owners']['owner_data']
            if len(owner_data) > 0:
                chart_file = self._create_owner_chart(owner_data, output_dir, prefix, chart_types)
                if chart_file:
                    chart_files.append(chart_file)
        
        # 分类统计图表
        if 'categories' in analysis and analysis['categories'].get('category_data'):
            category_data = analysis['categories']['category_data']
            if len(category_data) > 0:
                chart_file = self._create_category_chart(category_data, output_dir, prefix, chart_types)
                if chart_file:
                    chart_files.append(chart_file)
        
        # 物流统计图表
        if 'logistics' in analysis and analysis['logistics'].get('logistics_stats'):
            logistics_stats = analysis['logistics']['logistics_stats']
            if logistics_stats:
                chart_file = self._create_logistics_chart(logistics_stats, output_dir, prefix, chart_types)
                if chart_file:
                    chart_files.append(chart_file)
        
        # 每日趋势图表
        if 'daily' in analysis and analysis['daily'].get('daily_data'):
            daily_data = analysis['daily']['daily_data']
            if len(daily_data) > 0:
                chart_file = self._create_daily_chart(daily_data, output_dir, prefix)
                if chart_file:
                    chart_files.append(chart_file)
        
        return chart_files
    
    def _create_owner_chart(self, owner_data: List[Dict], output_dir: str, prefix: str, chart_types: List[str]) -> Optional[str]:
        """创建货主统计图表"""
        try:
            df = pd.DataFrame(owner_data)
            
            # 取前10个货主
            df_top = df.head(10)
            
            fig, ax = plt.subplots(figsize=(12, 8))
            
            if 'bar' in chart_types:
                bars = ax.bar(df_top['货主'], df_top['总数量'])
                ax.set_title('货主出货数量统计（前10名）', fontsize=16, fontweight='bold')
                ax.set_xlabel('货主', fontsize=12)
                ax.set_ylabel('出货数量', fontsize=12)
                
                # 添加数值标签
                for bar in bars:
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height,
                           f'{int(height)}', ha='center', va='bottom')
                
                plt.xticks(rotation=45, ha='right')
                plt.tight_layout()
                
                chart_file = os.path.join(output_dir, f"{prefix}_货主统计.png")
                plt.savefig(chart_file, dpi=300, bbox_inches='tight')
                plt.close()
                
                return chart_file
                
        except Exception as e:
            return None
    
    def _create_category_chart(self, category_data: List[Dict], output_dir: str, prefix: str, chart_types: List[str]) -> Optional[str]:
        """创建分类统计图表"""
        try:
            df = pd.DataFrame(category_data)
            
            fig, ax = plt.subplots(figsize=(10, 8))
            
            if 'pie' in chart_types:
                # 饼图
                ax.pie(df['总数量'], labels=df['分类'], autopct='%1.1f%%', startangle=90)
                ax.set_title('分类出货数量分布', fontsize=16, fontweight='bold')
            else:
                # 柱状图
                bars = ax.bar(df['分类'], df['总数量'])
                ax.set_title('分类出货数量统计', fontsize=16, fontweight='bold')
                ax.set_xlabel('分类', fontsize=12)
                ax.set_ylabel('出货数量', fontsize=12)
                
                # 添加数值标签
                for bar in bars:
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height,
                           f'{int(height)}', ha='center', va='bottom')
                
                plt.xticks(rotation=45, ha='right')
            
            plt.tight_layout()
            
            chart_file = os.path.join(output_dir, f"{prefix}_分类统计.png")
            plt.savefig(chart_file, dpi=300, bbox_inches='tight')
            plt.close()
            
            return chart_file
            
        except Exception as e:
            return None
    
    def _create_logistics_chart(self, logistics_stats: Dict, output_dir: str, prefix: str, chart_types: List[str]) -> Optional[str]:
        """创建物流统计图表"""
        try:
            companies = list(logistics_stats.keys())
            counts = list(logistics_stats.values())
            
            fig, ax = plt.subplots(figsize=(10, 6))
            
            if 'pie' in chart_types:
                # 饼图
                ax.pie(counts, labels=companies, autopct='%1.1f%%', startangle=90)
                ax.set_title('物流公司出货单量分布', fontsize=16, fontweight='bold')
            else:
                # 柱状图
                bars = ax.bar(companies, counts)
                ax.set_title('物流公司出货单量统计', fontsize=16, fontweight='bold')
                ax.set_xlabel('物流公司', fontsize=12)
                ax.set_ylabel('出货单量', fontsize=12)
                
                # 添加数值标签
                for bar in bars:
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height,
                           f'{int(height)}', ha='center', va='bottom')
            
            plt.tight_layout()
            
            chart_file = os.path.join(output_dir, f"{prefix}_物流统计.png")
            plt.savefig(chart_file, dpi=300, bbox_inches='tight')
            plt.close()
            
            return chart_file
            
        except Exception as e:
            return None
    
    def _create_daily_chart(self, daily_data: List[Dict], output_dir: str, prefix: str) -> Optional[str]:
        """创建每日趋势图表"""
        try:
            df = pd.DataFrame(daily_data)
            df['日期'] = pd.to_datetime(df['日期'])
            df = df.sort_values('日期')
            
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
            
            # 每日数量趋势
            ax1.plot(df['日期'], df['总数量'], marker='o', linewidth=2, markersize=6)
            ax1.set_title('每日出货数量趋势', fontsize=14, fontweight='bold')
            ax1.set_ylabel('出货数量', fontsize=12)
            ax1.grid(True, alpha=0.3)
            
            # 每日单量趋势
            ax2.plot(df['日期'], df['单号数量'], marker='s', linewidth=2, markersize=6, color='orange')
            ax2.set_title('每日出货单量趋势', fontsize=14, fontweight='bold')
            ax2.set_xlabel('日期', fontsize=12)
            ax2.set_ylabel('出货单量', fontsize=12)
            ax2.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            chart_file = os.path.join(output_dir, f"{prefix}_每日趋势.png")
            plt.savefig(chart_file, dpi=300, bbox_inches='tight')
            plt.close()
            
            return chart_file
            
        except Exception as e:
            return None
    
    def _get_month_last_day(self, year: int, month: int) -> int:
        """获取月份的最后一天"""
        if month == 12:
            next_month = datetime(year + 1, 1, 1)
        else:
            next_month = datetime(year, month + 1, 1)
        last_day = next_month - timedelta(days=1)
        return last_day.day

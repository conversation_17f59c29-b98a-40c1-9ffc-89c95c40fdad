"""
配置对话框
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
from typing import Dict, Any
from config import app_config

class ConfigDialog:
    """配置对话框"""
    
    def __init__(self, parent, config_manager):
        self.parent = parent
        self.config_manager = config_manager
        self.dialog = None
        
        # 配置变量
        self.config_vars = {}
        self._init_config_vars()
    
    def _init_config_vars(self):
        """初始化配置变量"""
        # API配置
        self.config_vars['api'] = {
            'auto_fetch_enabled': tk.BooleanVar(),
            'fetch_interval_minutes': tk.IntVar(),
            'timer_mode': tk.StringVar(),
            'daily_times': tk.StringVar()
        }
        
        # 路径配置
        self.config_vars['paths'] = {
            'output_path': tk.StringVar(),
            'archive_path': tk.StringVar(),
            'monthly_report_path': tk.StringVar()
        }
        
        # 监控配置
        self.config_vars['monitor'] = {
            'enabled': tk.<PERSON>anVar(),
            'auto_process': tk.Bo<PERSON>anVar(),
            'auto_send': tk.BooleanVar()
        }
        
        # 微信配置
        self.config_vars['wechat'] = {
            'contacts': tk.StringVar(),
            'send_as_image': tk.BooleanVar(),
            'send_statistics_message': tk.BooleanVar()
        }
        
        # UI配置
        self.config_vars['ui'] = {
            'theme': tk.StringVar(),
            'auto_start_monitor': tk.BooleanVar()
        }
    
    def show(self):
        """显示对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("系统配置")
        self.dialog.geometry("700x600")
        self.dialog.resizable(True, True)
        
        # 设置为模态对话框
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (
            self.parent.winfo_rootx() + 50,
            self.parent.winfo_rooty() + 50
        ))
        
        self._create_widgets()
        self._load_config()
    
    def _create_widgets(self):
        """创建界面组件"""
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建笔记本控件（标签页）
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建各个标签页
        self._create_api_tab(notebook)
        self._create_paths_tab(notebook)
        self._create_monitor_tab(notebook)
        self._create_wechat_tab(notebook)
        self._create_ui_tab(notebook)
        
        # 创建按钮区域
        self._create_button_area(main_frame)
    
    def _create_api_tab(self, notebook):
        """创建API配置标签页"""
        api_frame = ttk.Frame(notebook, padding="10")
        notebook.add(api_frame, text="API设置")
        
        # 自动获取设置
        ttk.Checkbutton(api_frame, text="启用API自动获取", 
                       variable=self.config_vars['api']['auto_fetch_enabled']).pack(anchor=tk.W, pady=(0, 10))
        
        # 获取间隔设置
        interval_frame = ttk.LabelFrame(api_frame, text="获取间隔", padding="10")
        interval_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(interval_frame, text="间隔时间（分钟）:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        interval_spin = ttk.Spinbox(interval_frame, from_=1, to=1440, width=10,
                                   textvariable=self.config_vars['api']['fetch_interval_minutes'])
        interval_spin.grid(row=0, column=1, sticky=tk.W)
        
        # 定时模式设置
        mode_frame = ttk.LabelFrame(api_frame, text="定时模式", padding="10")
        mode_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Radiobutton(mode_frame, text="间隔定时", value="interval",
                       variable=self.config_vars['api']['timer_mode']).pack(anchor=tk.W)
        ttk.Radiobutton(mode_frame, text="每日定时", value="daily",
                       variable=self.config_vars['api']['timer_mode']).pack(anchor=tk.W)
        
        # 每日执行时间设置
        daily_frame = ttk.LabelFrame(api_frame, text="每日执行时间", padding="10")
        daily_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(daily_frame, text="执行时间（用分号分隔，如：09:00;14:00;18:00）:").pack(anchor=tk.W, pady=(0, 5))
        ttk.Entry(daily_frame, textvariable=self.config_vars['api']['daily_times'], width=50).pack(fill=tk.X)
    
    def _create_paths_tab(self, notebook):
        """创建路径配置标签页"""
        paths_frame = ttk.Frame(notebook, padding="10")
        notebook.add(paths_frame, text="路径设置")
        
        # 输出路径
        output_frame = ttk.LabelFrame(paths_frame, text="输出路径", padding="10")
        output_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(output_frame, text="API数据输出文件:").pack(anchor=tk.W, pady=(0, 5))
        path_frame1 = ttk.Frame(output_frame)
        path_frame1.pack(fill=tk.X)
        ttk.Entry(path_frame1, textvariable=self.config_vars['paths']['output_path']).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(path_frame1, text="浏览", command=lambda: self._browse_file('paths', 'output_path', 'save')).pack(side=tk.RIGHT, padx=(5, 0))
        
        # 货品档案路径
        archive_frame = ttk.LabelFrame(paths_frame, text="货品档案", padding="10")
        archive_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(archive_frame, text="货品档案Excel文件:").pack(anchor=tk.W, pady=(0, 5))
        path_frame2 = ttk.Frame(archive_frame)
        path_frame2.pack(fill=tk.X)
        ttk.Entry(path_frame2, textvariable=self.config_vars['paths']['archive_path']).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(path_frame2, text="浏览", command=lambda: self._browse_file('paths', 'archive_path', 'open')).pack(side=tk.RIGHT, padx=(5, 0))
        
        # 月度报告路径
        monthly_frame = ttk.LabelFrame(paths_frame, text="月度报告", padding="10")
        monthly_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(monthly_frame, text="月度报告输出目录:").pack(anchor=tk.W, pady=(0, 5))
        path_frame3 = ttk.Frame(monthly_frame)
        path_frame3.pack(fill=tk.X)
        ttk.Entry(path_frame3, textvariable=self.config_vars['paths']['monthly_report_path']).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(path_frame3, text="浏览", command=lambda: self._browse_directory('paths', 'monthly_report_path')).pack(side=tk.RIGHT, padx=(5, 0))
    
    def _create_monitor_tab(self, notebook):
        """创建监控配置标签页"""
        monitor_frame = ttk.Frame(notebook, padding="10")
        notebook.add(monitor_frame, text="监控设置")
        
        # 监控选项
        ttk.Checkbutton(monitor_frame, text="启用文件监控", 
                       variable=self.config_vars['monitor']['enabled']).pack(anchor=tk.W, pady=(0, 10))
        
        ttk.Checkbutton(monitor_frame, text="文件变化时自动处理数据", 
                       variable=self.config_vars['monitor']['auto_process']).pack(anchor=tk.W, pady=(0, 10))
        
        ttk.Checkbutton(monitor_frame, text="数据处理完成后自动发送", 
                       variable=self.config_vars['monitor']['auto_send']).pack(anchor=tk.W, pady=(0, 10))
        
        # 监控说明
        info_frame = ttk.LabelFrame(monitor_frame, text="说明", padding="10")
        info_frame.pack(fill=tk.X, pady=(10, 0))
        
        info_text = """文件监控功能说明：
• 启用文件监控：程序会监控指定的Excel文件变化
• 自动处理数据：检测到文件变化时自动生成数据透视表
• 自动发送：数据处理完成后自动发送到微信联系人

注意：文件监控需要指定要监控的Excel文件路径"""
        
        ttk.Label(info_frame, text=info_text, justify=tk.LEFT).pack(anchor=tk.W)
    
    def _create_wechat_tab(self, notebook):
        """创建微信配置标签页"""
        wechat_frame = ttk.Frame(notebook, padding="10")
        notebook.add(wechat_frame, text="微信设置")
        
        # 联系人设置
        contacts_frame = ttk.LabelFrame(wechat_frame, text="联系人设置", padding="10")
        contacts_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(contacts_frame, text="微信联系人（用分号分隔多个联系人）:").pack(anchor=tk.W, pady=(0, 5))
        contacts_text = tk.Text(contacts_frame, height=3, wrap=tk.WORD)
        contacts_text.pack(fill=tk.X, pady=(0, 5))
        
        # 绑定文本框内容到变量
        def on_contacts_change(*args):
            self.config_vars['wechat']['contacts'].set(contacts_text.get(1.0, tk.END).strip())
        
        contacts_text.bind('<KeyRelease>', on_contacts_change)
        self.contacts_text = contacts_text  # 保存引用以便后续使用
        
        ttk.Label(contacts_frame, text="示例：张三;李四;王五", font=('TkDefaultFont', 8)).pack(anchor=tk.W)
        
        # 发送选项
        send_frame = ttk.LabelFrame(wechat_frame, text="发送选项", padding="10")
        send_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Checkbutton(send_frame, text="发送为图片格式（推荐）", 
                       variable=self.config_vars['wechat']['send_as_image']).pack(anchor=tk.W, pady=(0, 5))
        
        ttk.Checkbutton(send_frame, text="发送物流统计消息", 
                       variable=self.config_vars['wechat']['send_statistics_message']).pack(anchor=tk.W, pady=(0, 5))
        
        # 微信说明
        info_frame = ttk.LabelFrame(wechat_frame, text="使用说明", padding="10")
        info_frame.pack(fill=tk.X, pady=(10, 0))
        
        info_text = """微信发送功能说明：
• 确保微信客户端已登录并在前台运行
• 联系人名称必须与微信中显示的完全一致
• 发送图片格式需要安装相关依赖包
• 支持同时发送给多个联系人

注意：首次使用前建议先测试微信连接"""
        
        ttk.Label(info_frame, text=info_text, justify=tk.LEFT).pack(anchor=tk.W)
    
    def _create_ui_tab(self, notebook):
        """创建UI配置标签页"""
        ui_frame = ttk.Frame(notebook, padding="10")
        notebook.add(ui_frame, text="界面设置")
        
        # 主题设置
        theme_frame = ttk.LabelFrame(ui_frame, text="主题设置", padding="10")
        theme_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(theme_frame, text="界面主题:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        theme_combo = ttk.Combobox(theme_frame, textvariable=self.config_vars['ui']['theme'], 
                                  values=['light', 'dark'], state='readonly', width=15)
        theme_combo.grid(row=0, column=1, sticky=tk.W)
        
        # 启动选项
        startup_frame = ttk.LabelFrame(ui_frame, text="启动选项", padding="10")
        startup_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Checkbutton(startup_frame, text="程序启动时自动开始文件监控", 
                       variable=self.config_vars['ui']['auto_start_monitor']).pack(anchor=tk.W, pady=(0, 5))
        
        # 其他设置
        other_frame = ttk.LabelFrame(ui_frame, text="其他设置", padding="10")
        other_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(other_frame, text="更多界面设置功能将在后续版本中添加...").pack(anchor=tk.W)
    
    def _create_button_area(self, parent):
        """创建按钮区域"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 左侧按钮
        ttk.Button(button_frame, text="重置为默认", command=self._reset_to_default).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="导入配置", command=self._import_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="导出配置", command=self._export_config).pack(side=tk.LEFT)
        
        # 右侧按钮
        ttk.Button(button_frame, text="取消", command=self._cancel).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(button_frame, text="应用", command=self._apply).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(button_frame, text="确定", command=self._ok).pack(side=tk.RIGHT, padx=(10, 0))
    
    def _load_config(self):
        """加载配置到界面"""
        # API配置
        api_config = self.config_manager.get_api_config()
        self.config_vars['api']['auto_fetch_enabled'].set(api_config.get('auto_fetch_enabled', True))
        self.config_vars['api']['fetch_interval_minutes'].set(api_config.get('fetch_interval_minutes', 60))
        self.config_vars['api']['timer_mode'].set(api_config.get('timer_mode', 'daily'))
        
        daily_times = api_config.get('daily_times', [])
        self.config_vars['api']['daily_times'].set(';'.join(daily_times))
        
        # 路径配置
        paths_config = self.config_manager.get_paths_config()
        self.config_vars['paths']['output_path'].set(paths_config.get('output_path', ''))
        self.config_vars['paths']['archive_path'].set(paths_config.get('archive_path', ''))
        self.config_vars['paths']['monthly_report_path'].set(paths_config.get('monthly_report_path', ''))
        
        # 监控配置
        monitor_config = self.config_manager.get_monitor_config()
        self.config_vars['monitor']['enabled'].set(monitor_config.get('enabled', True))
        self.config_vars['monitor']['auto_process'].set(monitor_config.get('auto_process', True))
        self.config_vars['monitor']['auto_send'].set(monitor_config.get('auto_send', True))
        
        # 微信配置
        wechat_config = self.config_manager.get_wechat_config()
        contacts = wechat_config.get('contacts', [])
        self.config_vars['wechat']['contacts'].set(';'.join(contacts))
        self.contacts_text.delete(1.0, tk.END)
        self.contacts_text.insert(1.0, ';'.join(contacts))
        
        self.config_vars['wechat']['send_as_image'].set(wechat_config.get('send_as_image', True))
        self.config_vars['wechat']['send_statistics_message'].set(wechat_config.get('send_statistics_message', True))
        
        # UI配置
        ui_config = self.config_manager.get_ui_config()
        self.config_vars['ui']['theme'].set(ui_config.get('theme', 'light'))
        self.config_vars['ui']['auto_start_monitor'].set(ui_config.get('auto_start_monitor', False))
    
    def _save_config(self):
        """保存配置"""
        try:
            # API配置
            daily_times_str = self.config_vars['api']['daily_times'].get().strip()
            daily_times = [t.strip() for t in daily_times_str.split(';') if t.strip()] if daily_times_str else []
            
            api_config = {
                'auto_fetch_enabled': self.config_vars['api']['auto_fetch_enabled'].get(),
                'fetch_interval_minutes': self.config_vars['api']['fetch_interval_minutes'].get(),
                'timer_mode': self.config_vars['api']['timer_mode'].get(),
                'daily_times': daily_times
            }
            self.config_manager.set_api_config(api_config)
            
            # 路径配置
            paths_config = {
                'output_path': self.config_vars['paths']['output_path'].get(),
                'archive_path': self.config_vars['paths']['archive_path'].get(),
                'monthly_report_path': self.config_vars['paths']['monthly_report_path'].get()
            }
            self.config_manager.set_paths_config(paths_config)
            
            # 监控配置
            monitor_config = {
                'enabled': self.config_vars['monitor']['enabled'].get(),
                'auto_process': self.config_vars['monitor']['auto_process'].get(),
                'auto_send': self.config_vars['monitor']['auto_send'].get()
            }
            self.config_manager.set_monitor_config(monitor_config)
            
            # 微信配置
            contacts_str = self.contacts_text.get(1.0, tk.END).strip()
            contacts = [c.strip() for c in contacts_str.split(';') if c.strip()] if contacts_str else []
            
            wechat_config = {
                'contacts': contacts,
                'send_as_image': self.config_vars['wechat']['send_as_image'].get(),
                'send_statistics_message': self.config_vars['wechat']['send_statistics_message'].get()
            }
            self.config_manager.set_wechat_config(wechat_config)
            
            # UI配置
            ui_config = {
                'theme': self.config_vars['ui']['theme'].get(),
                'auto_start_monitor': self.config_vars['ui']['auto_start_monitor'].get()
            }
            self.config_manager.set_ui_config(ui_config)
            
            # 保存到文件
            self.config_manager.save_config()
            return True
            
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")
            return False
    
    def _browse_file(self, section: str, key: str, mode: str):
        """浏览文件"""
        if mode == 'open':
            filename = filedialog.askopenfilename(
                title="选择文件",
                filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
            )
        else:  # save
            filename = filedialog.asksaveasfilename(
                title="选择保存位置",
                defaultextension=".xlsx",
                filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
            )
        
        if filename:
            self.config_vars[section][key].set(filename)
    
    def _browse_directory(self, section: str, key: str):
        """浏览目录"""
        dirname = filedialog.askdirectory(title="选择目录")
        if dirname:
            self.config_vars[section][key].set(dirname)
    
    def _reset_to_default(self):
        """重置为默认配置"""
        if messagebox.askyesno("确认", "确定要重置为默认配置吗？这将清除所有当前设置。"):
            self.config_manager.reset_to_default()
            self._load_config()
            messagebox.showinfo("完成", "已重置为默认配置")
    
    def _import_config(self):
        """导入配置"""
        filename = filedialog.askopenfilename(
            title="选择配置文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if filename:
            if self.config_manager.import_config(filename):
                self._load_config()
                messagebox.showinfo("成功", "配置导入成功")
            else:
                messagebox.showerror("失败", "配置导入失败")
    
    def _export_config(self):
        """导出配置"""
        filename = filedialog.asksaveasfilename(
            title="保存配置文件",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if filename:
            if self.config_manager.export_config(filename):
                messagebox.showinfo("成功", "配置导出成功")
            else:
                messagebox.showerror("失败", "配置导出失败")
    
    def _apply(self):
        """应用配置"""
        if self._save_config():
            messagebox.showinfo("成功", "配置已保存")
    
    def _ok(self):
        """确定"""
        if self._save_config():
            self.dialog.destroy()
    
    def _cancel(self):
        """取消"""
        self.dialog.destroy()
